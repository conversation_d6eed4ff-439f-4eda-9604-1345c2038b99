# ---------------- 自动提权 ----------------
$CurrentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
$Principal    = New-Object System.Security.Principal.WindowsPrincipal($CurrentUser)
$IsAdmin      = $Principal.IsInRole([System.Security.Principal.WindowsBuiltInRole]::Administrator)
if (-not $IsAdmin) {
    Write-Host "当前 PowerShell 未以管理员身份运行，正在尝试提升权限……"
    Start-Process PowerShell -ArgumentList "-NoProfile -ExecutionPolicy Bypass -File `"$PSCommandPath`"" -Verb RunAs
    exit
}
Write-Host "已以管理员权限运行 PowerShell。"

# ---------------- 脚本版本 ----------------
$ScriptVersion = "0.9.7"
Write-Host "脚本版本：$ScriptVersion"

# ---------------- 检查 Hyper-V 模块 ----------------
if (-not (Get-Module -ListAvailable -Name Hyper-V)) {
    Write-Error "未检测到 Hyper-V 模块，请确认已安装 Hyper-V 功能。"
    exit
}

# ---------------- 统一优雅错误处理函数 ----------------
function Invoke-Graceful {
    param(
        [Parameter(Mandatory=$true)]
        [ScriptBlock]$Script,
        [string]$ErrorMessage = "操作失败",
        [string]$WarnMessage = "",
        [object]$Default = $null,
        [int]$DisplayDurationMs = 1500,  # 错误提示显示时间（毫秒），默认1.5秒
        [switch]$NoReturn  # 新增参数：是否抑制返回值
    )
    try {
        $result = & $Script
        if ($NoReturn) {
            return  # 不返回任何值
        } else {
            return $result
        }
    } catch {
        # 记录开始时间
        $startTime = Get-Date

        if ($WarnMessage) {
            Write-Warning $WarnMessage
        }
        Write-Warning "$ErrorMessage：$($_.Exception.Message)"

        # 确保错误提示显示足够长的时间
        $elapsedMs = ((Get-Date) - $startTime).TotalMilliseconds
        $remainingMs = $DisplayDurationMs - $elapsedMs

        if ($remainingMs -gt 0) {
            Start-Sleep -Milliseconds $remainingMs
        }

        if ($NoReturn) {
            return  # 不返回任何值
        } else {
            return $Default
        }
    }
}

# ---------------- 基础工具函数 ----------------
# 确保目录存在函数
function Ensure-Directory {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Path,
        [Parameter()]
        [string]$Description = "目录"
    )

    if (-not (Test-Path $Path)) {
        Write-Host "$Description 不存在，正在创建目录……" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path $Path -Force | Out-Null
    }
}

# ---------------- 配置持久化函数 ----------------
function Get-HyperVConfig {
    $configPath = Join-Path -Path $PSScriptRoot -ChildPath "HyperVConfig.json"
    if (Test-Path $configPath) {
        $json = Get-Content $configPath -Raw
        $cfg = ConvertFrom-Json $json
        # 兼容旧格式
        if ($cfg.ISOPath -is [string]) {
            $cfg.ISOPath = @($cfg.ISOPath)
        }
        return $cfg
    } else {
        $vmHost = Get-VMHost
        return [pscustomobject]@{
            VHDPath = $vmHost.VirtualHardDiskPath
            VMPath  = $vmHost.VirtualMachinePath
            ISOPath = @()
        }
    }
}

function Save-HyperVConfig($config) {
    $configPath = Join-Path -Path $PSScriptRoot -ChildPath "HyperVConfig.json"
    $json = $config | ConvertTo-Json -Depth 4
    $json | Out-File -FilePath $configPath -Encoding UTF8
}

# ---------------- 自动同步配置 ----------------
function Sync-HyperVConfig {
    $config = Get-HyperVConfig
    $vmHost = Get-VMHost
    $needsUpdate = $false
    $changes = @()

    # 检查并创建虚拟磁盘路径目录
    if ($config.VHDPath) {
        if (-not (Test-Path $config.VHDPath)) {
            $result = Invoke-Graceful {
                Ensure-Directory -Path $config.VHDPath -Description "虚拟磁盘路径"
                $true
            } "创建虚拟磁盘路径目录失败" "请确保您有足够的权限创建目录。" $false
            if ($result) {
                $changes += "虚拟磁盘路径目录已创建: $($config.VHDPath)"
                $needsUpdate = $true
            }
        }
        # 同步系统路径设置
        if ($vmHost.VirtualHardDiskPath -ne $config.VHDPath) {
            $result = Invoke-Graceful {
                Set-VMHost -VirtualHardDiskPath $config.VHDPath -ErrorAction Stop
                $true
            } "更新虚拟磁盘路径失败" "请确保您有足够的权限修改系统设置。" $false
            if ($result) {
                $changes += "虚拟磁盘路径已更新为: $($config.VHDPath)"
                $needsUpdate = $true
            }
        }
    }

    # 检查并创建虚拟机路径目录
    if ($config.VMPath) {
        if (-not (Test-Path $config.VMPath)) {
            $result = Invoke-Graceful {
                Ensure-Directory -Path $config.VMPath -Description "虚拟机路径"
                $true
            } "创建虚拟机路径目录失败" "请确保您有足够的权限创建目录。" $false
            if ($result) {
                $changes += "虚拟机路径目录已创建: $($config.VMPath)"
                $needsUpdate = $true
            }
        }
        # 同步系统路径设置
        if ($vmHost.VirtualMachinePath -ne $config.VMPath) {
            $result = Invoke-Graceful {
                Set-VMHost -VirtualMachinePath $config.VMPath -ErrorAction Stop
                $true
            } "更新虚拟机路径失败" "请确保您有足够的权限修改系统设置。" $false
            if ($result) {
                $changes += "虚拟机路径已更新为: $($config.VMPath)"
                $needsUpdate = $true
            }
        }
    }

    # 检查并创建 ISO 目录
    if ($config.ISOPath) {
        foreach ($isoPath in $config.ISOPath) {
            if (-not (Test-Path $isoPath)) {
                $result = Invoke-Graceful {
                    Ensure-Directory -Path $isoPath -Description "ISO 目录"
                    $true
                } "创建 ISO 目录失败" "请确保您有足够的权限创建目录。" $false
                if ($result) {
                    $changes += "ISO 目录已创建: $isoPath"
                    $needsUpdate = $true
                }
            }
        }
    }

    if ($needsUpdate) {
        Write-Host "`n配置已自动同步：" -ForegroundColor Green
        $changes | ForEach-Object { Write-Host "  $_" -ForegroundColor Cyan }
        Write-Host
    }
}

# 在脚本启动时执行自动同步
Write-Host "正在检查和同步配置..." -ForegroundColor Cyan
Sync-HyperVConfig

# ---------------- 路径获取工具函数 ----------------
# 获取有效的虚拟机路径（优先使用配置路径，否则使用系统默认路径）
function Get-EffectiveVMPath {
    $config = Get-HyperVConfig
    if ($config.VMPath -and (Test-Path $config.VMPath)) {
        return $config.VMPath
    } else {
        return (Get-VMHost).VirtualMachinePath
    }
}

# 获取有效的虚拟硬盘路径（优先使用配置路径，否则使用系统默认路径）
function Get-EffectiveVHDPath {
    $config = Get-HyperVConfig
    if ($config.VHDPath -and (Test-Path $config.VHDPath)) {
        return $config.VHDPath
    } else {
        return (Get-VMHost).VirtualHardDiskPath
    }
}

# ---------------- 用户输入工具函数 ----------------
# 带默认值的用户输入函数
function Read-HostWithDefault {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Prompt,
        [Parameter(Mandatory=$true)]
        $DefaultValue,
        [Parameter()]
        [type]$Type = [string]
    )

    $userInput = Read-Host $Prompt
    if ([string]::IsNullOrWhiteSpace($userInput)) {
        return $DefaultValue
    } else {
        return ($userInput -as $Type)
    }
}

# Y/N确认提示函数
function Confirm-YesNo {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Prompt,
        [Parameter()]
        [bool]$DefaultYes = $false
    )

    $defaultText = if ($DefaultYes) { "(Y/n)" } else { "(y/N)" }
    $fullPrompt = "$Prompt $defaultText"

    $response = Read-Host $fullPrompt

    if ([string]::IsNullOrWhiteSpace($response)) {
        return $DefaultYes
    }

    return $response -match '^(Y|y)$'
}

# ---------------- 通用交互函数模块 ----------------
# 通用选择函数，支持单选、无序多选和有序多选，以及不同的刷新策略
function Interactive-Selection {
    param(
        [Parameter(Mandatory=$true)]
        [array]$Items,
        [Parameter()]
        [ScriptBlock]$DisplayItem = { param($item) $item.ToString() },
        [Parameter()]
        [ScriptBlock]$TransformReturn = { param($item) $item },
        [string]$Header = "请选择项（使用上/下箭头移动，空格键切换选择状态，回车确认；按 ESC 返回；无选择则返回）：",
        [string]$Mode = "Multi", # Single: 单选, Multi: 无序多选, Ordered: 有序多选
        [string]$RefreshMode = "Partial", # Full: 每次全屏重绘, Partial: 仅重绘变化部分
        [string]$ExtraInfo = "", # 额外信息，仅在单选模式下使用
        [ScriptBlock]$CustomHeader = $null # 自定义头部显示函数
    )

    $count = $Items.Count
    if ($count -eq 0) {
        if ($Mode -eq "Single") {
            return @{ Result = $null; IsBack = $false }
        } else {
            return @()
        }
    }

    # 单选模式使用不同的逻辑
    if ($Mode -eq "Single") {
        $currentIndex = 0
        $selectedIndex = -1

        while ($true) {
            Clear-Host

            # 使用自定义头部或默认头部
            if ($CustomHeader) {
                & $CustomHeader
            } else {
                if (-not [string]::IsNullOrWhiteSpace($ExtraInfo)) {
                    Write-Host $ExtraInfo
                    Write-Host
                }
                Write-Host $Header -ForegroundColor Cyan
                Write-Host ""
            }

            for ($i = 0; $i -lt $count; $i++) {
                $marker = if ($selectedIndex -eq $i) { "[*]" } else { "[ ]" }
                $line = & $DisplayItem $Items[$i]
                if ($i -eq $currentIndex) {
                    Write-Host "-> $marker $line" -ForegroundColor Yellow
                } else {
                    Write-Host "   $marker $line"
                }
            }
            $key = [Console]::ReadKey($true)
            if ($key.Key -eq 'Enter') {
                break
            } elseif ($key.Key -eq 'Escape') {
                return @{ Result = $null; IsBack = $true }
            }
            switch ($key.Key) {
                "UpArrow" { $currentIndex--; if ($currentIndex -lt 0) { $currentIndex = $count - 1 } }
                "DownArrow" { $currentIndex = ($currentIndex + 1) % $count }
                "Spacebar" {
                    if ($selectedIndex -eq $currentIndex) {
                        $selectedIndex = -1
                    } else {
                        $selectedIndex = $currentIndex
                    }
                }
            }
        }
        if ($selectedIndex -ge 0) {
            return @{ Result = (& $TransformReturn $Items[$selectedIndex]); IsBack = $false }
        } else {
            return @{ Result = $null; IsBack = $false }
        }
    }

    # 多选模式的原有逻辑
    $selected = @()
    $selectedOrder = @()
    for ($i = 0; $i -lt $count; $i++) {
        $selected += $false
        $selectedOrder += 0
    }
    $orderCounter = 1
    $currentIndex = 0
    $previousIndex = -1 # 用于跟踪上次高亮的位置
    $cursorTop = [Console]::CursorTop # 记录调用时的光标位置

    # 1. 先构建初始屏幕内容
    $outputLines = @()
    $outputLines += $Header # 添加 Header
    $outputLines += ""      # 添加空行
    for ($i = 0; $i -lt $count; $i++) {
        $marker = if ($Mode -eq "Ordered" -and $selectedOrder[$i] -gt 0) { "[*] ($($selectedOrder[$i]))" } elseif ($selected[$i]) { "[*]" } else { "[ ]" }
        $line = & $DisplayItem $Items[$i]
        $prefix = if ($i -eq $currentIndex) { "-> " } else { "   " }
        $outputLines += "$prefix$marker $line"
    }

    # 2. 清屏
    Clear-Host

    # 3. 一次性输出构建好的内容
    Write-Host ($outputLines -join "`n")

    # 4. 计算列表项开始的行号 (Header占1行, 空行占1行, 所以列表从第3行开始，0-based index 为 2)
    $initialTop = 2

    # 5. 隐藏光标，准备接收输入
    [Console]::CursorVisible = $false
    $key = $null
    
    # 初始绘制后，强制进行一次当前行的绘制（带高亮）确保颜色正确
    [Console]::SetCursorPosition(0, $initialTop + $currentIndex)
    $currentMarker = if ($Mode -eq "Ordered" -and $selectedOrder[$currentIndex] -gt 0) { "[*] ($($selectedOrder[$currentIndex]))" } elseif ($selected[$currentIndex]) { "[*]" } else { "[ ]" }
    $currentLine = & $DisplayItem $Items[$currentIndex]
    Write-Host ("-> $currentMarker $currentLine" + " " * ([Console]::WindowWidth - ("-> $currentMarker $currentLine").Length)) -ForegroundColor Yellow
    $previousIndex = $currentIndex # 更新 previousIndex

    while ($key.Key -ne 'Enter' -and $key.Key -ne 'Escape') { # Escape 键退出
        if ($RefreshMode -eq "Full") {
            # 全屏重绘模式
            Clear-Host
            Write-Host $Header -ForegroundColor Cyan
            Write-Host ""
            for ($i = 0; $i -lt $count; $i++) {
                $marker = if ($Mode -eq "Ordered" -and $selectedOrder[$i] -gt 0) { "[*] ($($selectedOrder[$i]))" } elseif ($selected[$i]) { "[*]" } else { "[ ]" }
                $line = & $DisplayItem $Items[$i]
                if ($i -eq $currentIndex) {
                    Write-Host "-> $marker $line" -ForegroundColor Yellow
                } else {
                    Write-Host "   $marker $line"
                }
            }
        } else {
            # 部分重绘模式
            if ($currentIndex -ne $previousIndex) {
                # 重绘上一行（如果存在）
                if ($previousIndex -ge 0) {
                    [Console]::SetCursorPosition(0, $initialTop + $previousIndex)
                    $prevMarker = if ($Mode -eq "Ordered" -and $selectedOrder[$previousIndex] -gt 0) { "[*] ($($selectedOrder[$previousIndex]))" } elseif ($selected[$previousIndex]) { "[*]" } else { "[ ]" }
                    $prevLine = & $DisplayItem $Items[$previousIndex]
                    Write-Host ("   $prevMarker $prevLine" + " " * ([Console]::WindowWidth - ("   $prevMarker $prevLine").Length))
                }
                
                # 绘制当前行
                [Console]::SetCursorPosition(0, $initialTop + $currentIndex)
                $currentMarker = if ($Mode -eq "Ordered" -and $selectedOrder[$currentIndex] -gt 0) { "[*] ($($selectedOrder[$currentIndex]))" } elseif ($selected[$currentIndex]) { "[*]" } else { "[ ]" }
                $currentLine = & $DisplayItem $Items[$currentIndex]
                Write-Host ("-> $currentMarker $currentLine" + " " * ([Console]::WindowWidth - ("-> $currentMarker $currentLine").Length)) -ForegroundColor Yellow
                
                $previousIndex = $currentIndex
            }
        }
        
        $key = [Console]::ReadKey($true)
        
        switch ($key.Key) {
            "UpArrow"   {
                $previousIndex = $currentIndex
                $currentIndex--
                if ($currentIndex -lt 0) { $currentIndex = $count - 1 }
            }
            "DownArrow" {
                $previousIndex = $currentIndex
                $currentIndex = ($currentIndex + 1) % $count
            }
            "Spacebar"  {
                if ($Mode -eq "Ordered") {
                    if ($selectedOrder[$currentIndex] -eq 0) {
                        # 选择项目：分配当前计数器值
                        $selectedOrder[$currentIndex] = $orderCounter
                        $orderCounter++
                    } else {
                        # 取消选择项目：记录被取消的序号，然后调整后续序号
                        $canceledOrder = $selectedOrder[$currentIndex]
                        $selectedOrder[$currentIndex] = 0

                        # 将所有序号大于被取消序号的项目序号减1
                        for ($i = 0; $i -lt $count; $i++) {
                            if ($selectedOrder[$i] -gt $canceledOrder) {
                                $selectedOrder[$i]--
                            }
                        }

                        # 计数器减1
                        $orderCounter--
                    }
                } else {
                    $selected[$currentIndex] = -not $selected[$currentIndex]
                }
                if ($RefreshMode -eq "Partial") {
                    $previousIndex = -1 # 强制重绘当前行状态
                }
            }
            "Escape" { break } # Escape 键退出
        }
    }
    
    [Console]::CursorVisible = $true
    # 将光标移动到列表下方
    [Console]::SetCursorPosition(0, $initialTop + $count)
    Write-Host ""

    # 如果是按 Escape 退出，返回特殊标记或空数组，根据需要调整
    if ($key.Key -eq 'Escape') {
        return @()
    }

    if ($Mode -eq "Ordered") {
        $orderedSelections = @()
        for ($i = 0; $i -lt $count; $i++) {
            if ($selectedOrder[$i] -gt 0) {
                $orderedSelections += [pscustomobject]@{ Order = $selectedOrder[$i]; Item = $Items[$i] }
            }
        }
        $orderedSelections = $orderedSelections | Sort-Object Order
        return @($orderedSelections | ForEach-Object { & $TransformReturn $_.Item })
    } else {
        $selItems = @()
        for ($i = 0; $i -lt $count; $i++) {
            if ($selected[$i]) { $selItems += $Items[$i] }
        }
        return $selItems
    }
}

# ---------------- 通用 ISO 挂载函数 ----------------
function Mount-IsoCommon {
    param(
        [Parameter(Mandatory=$true)]
        $TargetVM,
        [string]$IsoDir
    )
    # 如果未提供 ISO 目录，则从配置中获取或提示输入
    $isoDirs = @()
    if (-not $IsoDir) {
        $config = Get-HyperVConfig
        if ($config.ISOPath -and $config.ISOPath.Count -gt 0) {
            $isoDirs = $config.ISOPath
            Write-Host "检测到默认 ISO 镜像目录：$($isoDirs -join ', ')"
        } else {
            while ($true) {
                $inputIso = Read-Host "请输入 ISO 镜像目录（直接回车结束）"
                if ([string]::IsNullOrWhiteSpace($inputIso)) { break }
                Ensure-Directory -Path $inputIso
                $isoDirs += $inputIso
            }
        }
    } else {
        $isoDirs = @($IsoDir)
    }
    if (-not $isoDirs -or $isoDirs.Count -eq 0) {
        Write-Host "未设置任何 ISO 镜像目录，操作已取消。"
        return
    }
    $isoFiles = @()
    foreach ($dir in $isoDirs) {
        if (Test-Path $dir) {
            $isoFiles += Get-ChildItem -Path $dir -Filter *.iso -ErrorAction SilentlyContinue
        }
    }
    if (-not $isoFiles -or $isoFiles.Count -eq 0) {
        Write-Host "所有目录中没有找到 ISO 文件。"
        return
    }
    # 交互选择 ISO，这里确保返回数组
    $header = "请选择要挂载的 ISO 镜像`n（使用上/下箭头移动，按空格键选择（记录顺序），回车确认, 无选择则返回）"
    $selectedIsoPaths = @(
        Interactive-Selection -Items $isoFiles -DisplayItem { param($iso) "$($iso.FullName)" } -TransformReturn { param($iso) $iso.FullName } -Header $header -Mode "Ordered" -RefreshMode "Full"
    )
    if ($selectedIsoPaths.Count -eq 0) {
        Write-Host "未选择任何 ISO 文件，跳过挂载操作。"
        return
    }

    # 获取并分析现有 DVD 驱动器状态
    $currentDVDs = @(Get-VMDvdDrive -VMName $TargetVM.Name -ErrorAction SilentlyContinue)
    $emptyDVDs = @($currentDVDs | Where-Object { [string]::IsNullOrWhiteSpace($_.Path) })
    $usedDVDs = @($currentDVDs | Where-Object { -not [string]::IsNullOrWhiteSpace($_.Path) })

    Write-Host "`n当前 DVD 驱动器状态：已使用 $($usedDVDs.Count) 个，空闲 $($emptyDVDs.Count) 个"

    # 清理多余的空 DVD 驱动器（保留1个）
    if ($emptyDVDs.Count -gt 1) {
        Write-Host "  正在清理 $($emptyDVDs.Count - 1) 个多余的空 DVD 驱动器..."
        $emptyDVDs[1..($emptyDVDs.Count - 1)] | ForEach-Object {
            Invoke-Graceful {
                Remove-VMDvdDrive -VMName $TargetVM.Name -ControllerNumber $_.ControllerNumber -ControllerLocation $_.ControllerLocation -ErrorAction Stop
                Write-Host "    ✓ 已清理空 DVD 驱动器 (控制器 $($_.ControllerNumber):$($_.ControllerLocation))" -ForegroundColor Green
            } "清理空 DVD 驱动器失败" -NoReturn
        }
        $emptyDVDs = @($emptyDVDs[0])  # 只保留第一个
    }

    # 挂载选中的 ISO 文件
    $mountedCount = 0
    foreach ($iso in $selectedIsoPaths) {
        # 跳过已挂载的 ISO
        if ($usedDVDs | Where-Object { $_.Path -eq $iso }) {
            Write-Host "ISO '$iso' 已挂载，跳过。"
            continue
        }

        # 优先使用空驱动器，否则创建新驱动器
        if ($emptyDVDs.Count -gt 0) {
            $targetDVD = $emptyDVDs[0]
            $result = Invoke-Graceful {
                Set-VMDvdDrive -VMName $TargetVM.Name -ControllerNumber $targetDVD.ControllerNumber -ControllerLocation $targetDVD.ControllerLocation -Path $iso -ErrorAction Stop
                Write-Host "ISO '$iso' 已挂载到现有驱动器。" -ForegroundColor Cyan
                $true
            } "挂载 ISO 到现有驱动器失败" "" $false
            if ($result) { $emptyDVDs = @($emptyDVDs[1..($emptyDVDs.Count - 1)]) }  # 移除已使用的驱动器
        } else {
            $result = Invoke-Graceful {
                Add-VMDvdDrive -VMName $TargetVM.Name -Path $iso -ErrorAction Stop | Out-Null
                Write-Host "ISO '$iso' 已挂载到新驱动器。" -ForegroundColor Cyan
                $true
            } "挂载 ISO 失败" "" $false
        }
        if ($result) { $mountedCount++ }
    }

    if ($mountedCount -eq 0) {
        Write-Host "没有成功挂载任何 ISO 文件。" -ForegroundColor Red
        return
    }
    Write-Host "`n成功挂载了 $mountedCount 个 ISO 文件。" -ForegroundColor Green

    $setBootInput = Read-Host "是否将第一个挂载的 ISO 设置为第一启动设备? (Y/n)"
    if ([string]::IsNullOrWhiteSpace($setBootInput) -or $setBootInput -match '^(Y|y)$') {
         $firstIso = $selectedIsoPaths[0]
         $dvdDrives = Get-VMDvdDrive -VMName $TargetVM.Name -ErrorAction SilentlyContinue
         if (-not $dvdDrives -or $dvdDrives.Count -eq 0) {
             Write-Host "未找到 DVD 驱动器，无法设置启动顺序。" -ForegroundColor Yellow
             return
         }
         $firstDvd = $dvdDrives | Where-Object { $_.Path -eq $firstIso } | Select-Object -First 1
         if (-not $firstDvd -and $dvdDrives.Count -eq 1) {
             $firstDvd = $dvdDrives[0]
         }
         if ($firstDvd) {
             Invoke-Graceful {
                  Set-VMFirmware -VMName $TargetVM.Name -FirstBootDevice $firstDvd -ErrorAction Stop
                  Write-Host "已将 ISO '$firstIso' 设置为第一启动设备。" -ForegroundColor Green
                  $true
             } "设置启动项失败" "" $false -NoReturn
         } else {
             Write-Host "未能获取挂载的 DVD 设备，无法设置启动顺序。" -ForegroundColor Yellow
         }
    }
}

# ---------------- 新增：打开 Hyper-V 控制台 ----------------
function Open-HyperVConsole {
    Write-Host "正在打开 Hyper-V 管理控制台……"
    Invoke-Graceful {
        Start-Process -FilePath "mmc.exe" -ArgumentList "virtmgmt.msc" -ErrorAction Stop
        Write-Host "已成功启动 Hyper-V 管理控制台。"
    } "启动 Hyper-V 管理控制台失败" -NoReturn
}

# ---------------- 功能函数 ----------------
function Manage-VM {
    $options = @(
        [pscustomobject]@{ Label = "创建"; Action = { Create-VMCustom } },
        [pscustomobject]@{ Label = "复制"; Action = { Copy-VM } },
        [pscustomobject]@{ Label = "删除"; Action = { Remove-VMAndDisks } },
        [pscustomobject]@{ Label = "导入"; Action = { Import-VMCustom } },
        [pscustomobject]@{ Label = "导出"; Action = { Export-VMCustom } }
    )
    $selection = Interactive-Selection -Items $options -DisplayItem { param($opt) "$($opt.Label)" } -TransformReturn { param($opt) $opt } -Header "请选择虚拟机管理操作`n（使用上/下箭头移动，按空格键选择（记录顺序），回车确认, 无选择则返回上级）" -Mode "Ordered" -RefreshMode "Full"
    if (-not $selection -or $selection.Count -eq 0) {
         Write-Host "未选择任何操作，返回上级。"
         return
    }
    foreach ($sel in $selection) {
        & $sel.Action
    }
}

function Create-VMCustom {
    $vmName = Read-Host "请输入虚拟机名称"
    if ([string]::IsNullOrWhiteSpace($vmName)) {
        Write-Host "虚拟机名称不能为空，操作已取消。"
        return
    }
    if (Get-VM -Name $vmName -ErrorAction SilentlyContinue) {
        Write-Host "虚拟机 '$vmName' 已存在，请选择其他名称。"
        return
    }
    
    $memGB = Read-HostWithDefault "请输入启动内存 (GB) [默认: 4]" 4 ([double])
    
    $procCount = Read-HostWithDefault "请输入处理器数量 [默认: 8]" 8 ([int])
    
    $diskSizeGB = Read-HostWithDefault "请输入虚拟硬盘大小 (GB) [默认: 120]" 120 ([double])
    
    $memBytes = $memGB * 1GB; $diskBytes = $diskSizeGB * 1GB

    $result = Invoke-Graceful {
        # 获取配置的虚拟机路径
        $vmBasePath = Get-EffectiveVMPath

        # 使用-Path参数直接指定基础路径（不包含虚拟机名称）
        # Hyper-V会自动在此路径下创建以虚拟机名称命名的目录
        New-VM -Name $vmName -MemoryStartupBytes $memBytes -Generation 2 -Path $vmBasePath -ErrorAction Stop | Out-Null
        $true
    } "虚拟机创建失败" "" $false
    if (-not $result) { return }

    Write-Host "虚拟机 '$vmName' 已成功创建。"

    Invoke-Graceful {
        $defaultNics = Get-VMNetworkAdapter -VMName $vmName -ErrorAction SilentlyContinue
        if ($defaultNics) {
            $defaultNics | Remove-VMNetworkAdapter -ErrorAction SilentlyContinue
            Write-Host "已移除虚拟机 '$vmName' 的默认网络适配器。"
        }
    } "未检测到默认网络适配器或移除失败" -NoReturn
    
    Invoke-Graceful {
        Set-VMFirmware -VMName $vmName -EnableSecureBoot ([Microsoft.HyperV.PowerShell.OnOffState]::Off) -ErrorAction Stop
        Write-Host "安全启动已关闭。"
    } "关闭安全启动失败" -NoReturn

    Invoke-Graceful {
        Set-VM -Name $vmName -AutomaticCheckpointsEnabled $false -ErrorAction Stop
        Write-Host "自动检查点功能已禁用。"
    } "禁用自动检查点失败" -NoReturn

    Invoke-Graceful {
        Set-VMProcessor -VMName $vmName -Count $procCount -ExposeVirtualizationExtensions $true -ErrorAction Stop
        Write-Host "已配置处理器数为 $procCount，并启用嵌套虚拟化。"
    } "处理器配置或嵌套虚拟化设置失败" -NoReturn
    
    $result = Invoke-Graceful { 
        $vmHost = Get-VMHost
        $defaultPath = $vmHost.VirtualHardDiskPath
        $diskPath = Join-Path -Path $defaultPath -ChildPath "$vmName.vhdx"
        New-VHD -Path $diskPath -SizeBytes $diskBytes -Dynamic -ErrorAction Stop | Out-Null
        Write-Host "虚拟硬盘 '$diskPath' (大小: $diskSizeGB GB) 已成功创建。"
        Add-VMHardDiskDrive -VMName $vmName -Path $diskPath -ErrorAction Stop
        Write-Host "虚拟硬盘已成功挂载到虚拟机。"
        $true
    } "虚拟硬盘创建或挂载失败" "" $false
    
    $result = Invoke-Graceful { 
        $switches = Get-VMSwitch
        if (-not $switches -or $switches.Count -eq 0) {
            Write-Host "未找到可用的虚拟交换机，跳过网络配置。"
            return $true
        }
        $header = "请选择要添加网络适配器的交换机`n（使用上/下箭头移动，按空格键选择（记录顺序），回车确认, 无选择则返回）"
        $selectedSwitches = Interactive-Selection -Items $switches -DisplayItem { param($sw) "$($sw.Name)" } -TransformReturn { param($sw) $sw } -Header $header -Mode "Ordered" -RefreshMode "Full"
        if (-not $selectedSwitches -or $selectedSwitches.Count -eq 0) {
            Write-Host "未选择任何交换机，跳过网络配置。"
            return $true
        }
        foreach ($selected in $selectedSwitches) {
            Add-VMNetworkAdapter -VMName $vmName -SwitchName $selected.Name -ErrorAction Stop | Out-Null
            $adapter = (Get-VMNetworkAdapter -VMName $vmName | Where-Object { $_.SwitchName -eq $selected.Name }) | Select-Object -Last 1
            Set-VMNetworkAdapter -VMName $vmName -Name $adapter.Name -MacAddressSpoofing On -ErrorAction Stop
            Write-Host "已为虚拟机添加网络适配器（交换机: $($selected.Name)）并启用 MAC 欺骗。"
        }
        $true
    } "网络配置失败" "" $false
    
    # 调用通用 ISO 挂载函数
    if (Confirm-YesNo "是否挂载 ISO 镜像?" $true) {
         Mount-IsoCommon -TargetVM (Get-VM -Name $vmName)
    }

    Write-Host "虚拟机 '$vmName' 已成功创建并完成优化配置。"

    # 清理空目录
    Remove-EmptyDirectories -OperationType "创建"
}

# 清理虚拟机相关的空目录
function Remove-EmptyDirectories {
    param(
        [Parameter()]
        [string]$RootPath,  # 可选参数，如果不提供则自动获取VM路径
        [Parameter()]
        [string]$OperationType  # 操作类型：创建、复制、删除、导入等，用于个性化消息
    )

    # 如果没有提供路径，自动获取VM路径
    if ([string]::IsNullOrWhiteSpace($RootPath)) {
        $RootPath = Get-EffectiveVMPath
    }

    if (-not (Test-Path $RootPath)) {
        Write-Host "路径 '$RootPath' 不存在，跳过空目录清理。" -ForegroundColor Yellow
        return
    }

    # 显示开始清理的消息
    if ($OperationType) {
        Write-Host "`n正在清理空目录..." -ForegroundColor Cyan
    } else {
        Write-Host "正在清理空目录：$RootPath" -ForegroundColor Cyan
    }

    $removedCount = 0

    # 递归获取所有目录，按深度倒序排列（先删除深层目录）
    $directories = Invoke-Graceful {
        Get-ChildItem -Path $RootPath -Directory -Recurse -ErrorAction Stop |
        Sort-Object { $_.FullName.Split([System.IO.Path]::DirectorySeparatorChar).Count } -Descending
    } "获取目录列表失败" "" @()

    foreach ($dir in $directories) {
        # 检查目录是否为空（没有文件和子目录）
        $isEmpty = Invoke-Graceful {
            $items = Get-ChildItem -Path $dir.FullName -Force -ErrorAction Stop
            $items.Count -eq 0
        } "检查目录是否为空失败" "" $false

        if ($isEmpty) {
            $removeResult = Invoke-Graceful {
                Remove-Item -Path $dir.FullName -Force -ErrorAction Stop
                $true
            } "" "" $false

            if ($removeResult) {
                Write-Host "  ✓ 已删除空目录：$($dir.FullName)" -ForegroundColor Green
                $removedCount++
            } else {
                Write-Host "  ✗ 删除空目录失败：$($dir.FullName)" -ForegroundColor Red
            }
        }
    }

    # 显示完成消息
    if ($removedCount -gt 0) {
        Write-Host "空目录清理完成，共删除 $removedCount 个空目录。" -ForegroundColor Green
    } else {
        Write-Host "未发现空目录，无需清理。" -ForegroundColor Gray
    }

    # 如果提供了操作类型，显示操作完成消息
    if ($OperationType) {
        Write-Host "虚拟机${OperationType}和清理操作完成。" -ForegroundColor Green
    }
}

function Remove-VMAndDisks {
    $vms = Invoke-Graceful { Get-VM | Sort-Object Name } "获取虚拟机列表失败" "" @()
    if (-not $vms -or $vms.Count -eq 0) {
        Write-Host "当前未检测到任何虚拟机，请先创建一个虚拟机。"
        return
    }
    $header = "请选择要删除的虚拟机`n（使用上/下箭头移动，按空格键切换选择状态，回车确认, 无选择则返回）"
    $selectedVMs = Interactive-Selection -Items $vms -DisplayItem { param($vm) "$($vm.Name) - $($vm.State)" } -Header $header -Mode "Multi" -RefreshMode "Full"
    if (-not $selectedVMs -or $selectedVMs.Count -eq 0) {
         Write-Host "未选择任何虚拟机，返回上级。"
         return
    }
    foreach ($vm in $selectedVMs) {
         Write-Host "`n正在处理虚拟机: $($vm.Name)…"
         if ($vm.State -eq "Running") {
             Write-Host "虚拟机 '$($vm.Name)' 正在运行，需要先停止才能删除。"
             if (Confirm-YesNo "是否停止该虚拟机?" $false) {
                 Invoke-Graceful { Stop-VM -VMName $vm.Name -Force -ErrorAction Stop; Start-Sleep -Seconds 3 } "停止虚拟机失败" -NoReturn
                 $vmRef = Invoke-Graceful { Get-VM -Name $vm.Name -ErrorAction SilentlyContinue } "获取虚拟机状态失败" "" $null
                 if (-not $vmRef -or $vmRef.State -ne "Off") {
                     Write-Host "虚拟机 '$($vm.Name)' 无法成功停止，跳过删除。"
                     continue
                 }
             } else {
                 Write-Host "跳过虚拟机 '$($vm.Name)' 的删除处理。"
                 continue
             }
         }
         $disks = Invoke-Graceful { Get-VMHardDiskDrive -VMName $vm.Name -ErrorAction Stop } "获取虚拟机磁盘信息失败" "" @()
         if (-not $disks -or $disks.Count -eq 0) {
             Write-Host "虚拟机 '$($vm.Name)' 没有挂载磁盘。"
         } elseif ($disks.Count -eq 1) {
             Write-Host "虚拟机 '$($vm.Name)' 挂载的磁盘："
             Write-Host "  $($disks[0].Path)"
             if (Test-Path $disks[0].Path) {
                 Invoke-Graceful { Remove-Item -Path $disks[0].Path -Force -ErrorAction Stop } "删除磁盘失败" -NoReturn
                 Write-Host "磁盘已删除。"
             } else {
                 Write-Host "磁盘 '$($disks[0].Path)' 不存在，跳过删除。"
             }
         } elseif ($disks.Count -gt 1) {
             Write-Host "虚拟机 '$($vm.Name)' 挂载了多个磁盘。"
             $header = "请选择要删除的磁盘`n（使用上/下箭头移动，按空格键切换选择状态，回车确认, 无选择则返回）"
             $selDisks = Interactive-Selection -Items $disks -DisplayItem { param($disk) "$($disk.Path)" } -Header $header -Mode "Multi" -RefreshMode "Full"
             if ($selDisks -and $selDisks.Count -gt 0) {
                  foreach ($d in $selDisks) {
                      if (Test-Path $d.Path) {
                           Invoke-Graceful { Remove-Item -Path $d.Path -Force -ErrorAction Stop } "删除磁盘失败" -NoReturn
                           Write-Host "已删除磁盘：$($d.Path)"
                      } else {
                           Write-Host "磁盘 '$($d.Path)' 不存在，跳过删除。"
                      }
                  }
             } else {
                  Write-Host "未选择磁盘，跳过虚拟机 '$($vm.Name)' 的删除处理。"
                  continue
             }
         }
         Invoke-Graceful { Remove-VM -VMName $vm.Name -Force -ErrorAction Stop } "删除虚拟机失败" -NoReturn
         Write-Host "虚拟机 '$($vm.Name)' 已成功删除。"
    }

    # 清理空目录
    Remove-EmptyDirectories -OperationType "删除"
}

function Toggle-Nested {
    $vms = Get-VM | Sort-Object Name
    if (-not $vms -or $vms.Count -eq 0) {
        Write-Host "当前未检测到任何虚拟机，请先创建一个虚拟机。"
        return
    }
    $vmResult = Interactive-Selection -Items $vms -DisplayItem { param($vm) "$($vm.Name) - $($vm.State)" } -Header "请选择虚拟机`n（使用上/下箭头移动，按空格键选择，回车确认, 未选择则返回）" -Mode "Single"
    if ($vmResult.IsBack -or -not $vmResult.Result) {
        Write-Host "返回上级。"
        return
    }
    $vm = $vmResult.Result
    $proc = Invoke-Graceful { Get-VMProcessor -VMName $vm.Name -ErrorAction Stop } "获取处理器信息失败" "" $null
    if (-not $proc) { return }
    if ($proc.ExposeVirtualizationExtensions) {
        Invoke-Graceful {
            Set-VMProcessor -ExposeVirtualizationExtensions $false -VMName $vm.Name -ErrorAction Stop
            Write-Host "虚拟机 '$($vm.Name)' 的嵌套虚拟化已关闭。"
        } "关闭失败" -NoReturn
    } else {
        Invoke-Graceful {
            Set-VMProcessor -ExposeVirtualizationExtensions $true -VMName $vm.Name -ErrorAction Stop
            Write-Host "虚拟机 '$($vm.Name)' 的嵌套虚拟化已开启。"
        } "开启失败" -NoReturn
    }
}

function Toggle-Resource {
    $vms = Get-VM | Sort-Object Name
    if (-not $vms -or $vms.Count -eq 0) {
        Write-Host "当前未检测到任何虚拟机，请先创建一个虚拟机。"
        return
    }
    $vmResult = Interactive-Selection -Items $vms -DisplayItem { param($vm) "$($vm.Name) - $($vm.State)" } -Header "请选择虚拟机`n（使用上/下箭头移动，按空格键选择，回车确认, 未选择则返回）" -Mode "Single"
    if ($vmResult.IsBack -or -not $vmResult.Result) {
        Write-Host "返回上级。"
        return
    }
    $vm = $vmResult.Result
    if ($vm.ResourceMeteringEnabled) {
        Invoke-Graceful {
            Disable-VMResourceMetering -VMName $vm.Name -ErrorAction Stop
            Write-Host "虚拟机 '$($vm.Name)' 的资源计量已关闭。"
        } "关闭失败" -NoReturn
    } else {
        Invoke-Graceful {
            Enable-VMResourceMetering -VMName $vm.Name -ErrorAction Stop
            Write-Host "虚拟机 '$($vm.Name)' 的资源计量已开启。"
        } "开启失败" -NoReturn
    }
}

function ListVMs {
    $vms = Get-VM | Sort-Object Name
    if (-not $vms -or $vms.Count -eq 0) { Write-Host "当前未检测到任何虚拟机。"; return }
    Write-Host "`n虚拟机状态列表：" -ForegroundColor Cyan
    Write-Host "==========================================`n"

    $stateMap = @{ "Running" = "运行"; "Off" = "关闭"; "Saved" = "已保存"; "Paused" = "暂停" }
    $list = $vms | ForEach-Object {
        $vm = $_
        $proc = Invoke-Graceful { Get-VMProcessor -VMName $vm.Name -ErrorAction Stop } "获取处理器信息失败" "" $null
        $processorCount = if ($proc) { $proc.Count } else { "-" }
        $memAllocated = if ($vm.MemoryStartupBytes -gt 0) { [math]::Round($vm.MemoryStartupBytes / 1GB, 2) } else { 4 }
        $disks = Invoke-Graceful { @(Get-VMHardDiskDrive -VMName $vm.Name -ErrorAction Stop) } "获取磁盘信息失败" "" @()
        $diskInfo = if ($disks -and $disks.Count -gt 0) { $diskCount = $disks.Count; $totalDiskSizeGB = [math]::Round((($disks | Where-Object { Test-Path $_.Path } | ForEach-Object { (Get-Item $_.Path -ErrorAction SilentlyContinue).Length } | Measure-Object -Sum).Sum / 1GB), 2); if ($totalDiskSizeGB -gt 0) { "${diskCount}块 ($totalDiskSizeGB GB)" } else { "${diskCount}块" } } else { "-" }
        $switchName = (Get-VMNetworkAdapter -VMName $vm.Name -ErrorAction SilentlyContinue | Select-Object -First 1 | ForEach-Object { $_.SwitchName }) -join "" -replace "^$", "-"
        $vmState = if ($stateMap.ContainsKey($vm.State)) { $stateMap[$vm.State] } else { $vm.State }

        [pscustomobject]@{
            "名称" = $vm.Name
            "状态" = $vmState
            "处理器" = if ($processorCount -eq "-") { "-" } else { "${processorCount}核" }
            "内存" = "$memAllocated GB"
            "嵌套" = if ($proc -and $proc.ExposeVirtualizationExtensions) { "√" } else { "-" }
            "检查点" = if ($vm.AutomaticCheckpointsEnabled) { "√" } else { "-" }
            "计量" = if ($vm.ResourceMeteringEnabled) { "√" } else { "-" }
            "网络" = $switchName
            "磁盘" = $diskInfo
        }
    }

    $list | Format-Table @{Label="名称";Expression={$_.名称};Width=20}, @{Label="状态";Expression={$_.状态};Width=8}, @{Label="处理器";Expression={$_.处理器};Width=8}, @{Label="内存";Expression={$_.内存};Width=10}, @{Label="嵌套";Expression={$_.嵌套};Width=6}, @{Label="检查点";Expression={$_.检查点};Width=8}, @{Label="计量";Expression={$_.计量};Width=6}, @{Label="网络";Expression={$_.网络};Width=12}, @{Label="磁盘";Expression={$_.磁盘};Width=25} -AutoSize

    Write-Host "`n说明：" -ForegroundColor Yellow
    Write-Host "√ = 已启用  - = 未启用/无`n"
}

function Copy-VM {
    $vms = Invoke-Graceful { Get-VM | Sort-Object Name } "获取虚拟机列表失败" "" @()
    if (-not $vms -or $vms.Count -eq 0) {
        Write-Host "当前未检测到任何虚拟机，请先创建一个虚拟机。"
        return
    }
    $sourceVMResult = Interactive-Selection -Items $vms -DisplayItem { param($vm) "$($vm.Name) - $($vm.State)" } -Header "请选择虚拟机`n（使用上/下箭头移动，按空格键选择，回车确认, 未选择则返回）" -Mode "Single"
    if ($sourceVMResult.IsBack -or -not $sourceVMResult.Result) {
        Write-Host "未选择源虚拟机或已取消操作。"
        return
    }
    $sourceVM = $sourceVMResult.Result
    if ($sourceVM.State -eq "Running") {
        Write-Host "源虚拟机正在运行。为确保数据一致性，建议先关闭虚拟机。"
        if (Confirm-YesNo "是否关闭源虚拟机?" $false) {
            Invoke-Graceful { Stop-VM -Name $sourceVM.Name -Force -ErrorAction Stop } "关闭虚拟机失败" -NoReturn
            Write-Host "正在等待虚拟机 '$($sourceVM.Name)' 关闭..."
            while ((Invoke-Graceful { (Get-VM -Name $sourceVM.Name).State } "获取虚拟机状态失败" "" "Off") -ne "Off") {
                Start-Sleep -Seconds 1
            }
            $sourceVM = Invoke-Graceful { Get-VM -Name $sourceVM.Name } "刷新虚拟机状态失败" "" $sourceVM
            Write-Host "虚拟机 '$($sourceVM.Name)' 已关闭。"
        } else {
            Write-Host "继续复制运行中的虚拟机可能会导致数据不一致。"
            if (-not (Confirm-YesNo "是否继续?" $false)) {
                Write-Host "操作已取消。"
                return
            }
        }
    }
    $newName = Read-Host "请输入新虚拟机的名称"
    if ([string]::IsNullOrWhiteSpace($newName)) {
        Write-Host "虚拟机名称不能为空，操作已取消。"
        return
    }
    if (Invoke-Graceful { Get-VM -Name $newName -ErrorAction SilentlyContinue } "检测虚拟机重名失败" "" $null) {
        Write-Host "虚拟机 '$newName' 已存在，请选择其他名称。"
        return
    }
    $sourceDisks = Invoke-Graceful { Get-VMHardDiskDrive -VMName $sourceVM.Name -ErrorAction Stop } "获取源虚拟机磁盘信息失败" "" @()
    $sourceNics = Invoke-Graceful { Get-VMNetworkAdapter -VMName $sourceVM.Name } "获取源虚拟机网卡失败" "" @()
    $sourceDVDs = Invoke-Graceful { Get-VMDvdDrive -VMName $sourceVM.Name } "获取源虚拟机DVD失败" "" @()
    $sourceFirmware = Invoke-Graceful { Get-VMFirmware -VMName $sourceVM.Name -ErrorAction SilentlyContinue } "获取源固件失败" "" $null
    $sourceProcessor = Invoke-Graceful { Get-VMProcessor -VMName $sourceVM.Name } "获取源处理器失败" "" $null
    $memoryStartupBytes = $sourceVM.MemoryStartup
    if (-not $memoryStartupBytes -or $memoryStartupBytes -eq 0) {
        $memoryStartupBytes = 4GB
        Write-Host "未能获取源虚拟机的内存配置，将使用默认值 $([math]::Round($memoryStartupBytes/1GB, 2)) GB。" -ForegroundColor Yellow
    }
    $vhdPath = Get-EffectiveVHDPath; $vmPath = Get-EffectiveVMPath
    Invoke-Graceful {
        # 使用-Path参数直接指定基础路径（不包含虚拟机名称）
        # Hyper-V会自动在此路径下创建以虚拟机名称命名的目录
        $newVM = New-VM -Name $newName -MemoryStartupBytes $memoryStartupBytes -Generation $sourceVM.Generation -NoVHD -Path $vmPath -ErrorAction Stop

        if (-not $newVM) { throw "创建新虚拟机对象失败" }
        foreach ($sourceDiskDrive in $sourceDisks) {
            $sourceVhdPath = $sourceDiskDrive.Path
            if (-not (Test-Path $sourceVhdPath)) {
                Write-Host "源磁盘路径 '$sourceVhdPath' 不存在，跳过此磁盘。"
                continue
            }
            $sourceVhdName = [System.IO.Path]::GetFileNameWithoutExtension($sourceVhdPath)
            $vhdExtension = [System.IO.Path]::GetExtension($sourceVhdPath)
            $newVhdFileName = "$newName$vhdExtension"
            if ($sourceDiskDrive -ne $sourceDisks[0]) {
                 $newVhdFileName = "${newName}_disk$($sourceDisks.IndexOf($sourceDiskDrive))$vhdExtension"
            }
            $newVhdPath = Join-Path -Path $vhdPath -ChildPath $newVhdFileName
            $counter = 1
            while (Test-Path $newVhdPath) {
                $newVhdFileName = "${newName}_disk$($sourceDisks.IndexOf($sourceDiskDrive))_$counter$vhdExtension"
                if ($sourceDiskDrive -eq $sourceDisks[0] -and $counter -eq 1) {
                    $newVhdFileName = "${newName}_$counter$vhdExtension"
                }
                $newVhdPath = Join-Path -Path $vhdPath -ChildPath $newVhdFileName
                $counter++
            }
            Write-Host "正在复制磁盘:"
            Write-Host "  源路径: $sourceVhdPath"
            Write-Host "  目标路径: $newVhdPath"
            Invoke-Graceful { Copy-Item -Path $sourceVhdPath -Destination $newVhdPath -Force -ErrorAction Stop } "复制磁盘失败" -NoReturn
            Invoke-Graceful { Add-VMHardDiskDrive -VMName $newName -Path $newVhdPath -ControllerType $sourceDiskDrive.ControllerType -ControllerNumber $sourceDiskDrive.ControllerNumber -ControllerLocation $sourceDiskDrive.ControllerLocation -ErrorAction Stop } "挂载磁盘失败" -NoReturn
        }
        Invoke-Graceful { Set-VMProcessor -VMName $newName -Count $sourceProcessor.Count -ExposeVirtualizationExtensions $sourceProcessor.ExposeVirtualizationExtensions -ErrorAction Stop } "配置处理器失败" -NoReturn
        $existingNics = Invoke-Graceful { Get-VMNetworkAdapter -VMName $newName -ErrorAction SilentlyContinue } "获取新虚拟机网卡失败" "" @()
        if ($existingNics) { $existingNics | ForEach-Object { Invoke-Graceful { Remove-VMNetworkAdapter -VMName $newName -Name $_.Name -ErrorAction SilentlyContinue } "移除默认网卡失败" -NoReturn } }
        foreach ($nic in $sourceNics) {
            $newNicParams = @{ VMName = $newName }
            if ($nic.SwitchName) {
                $newNicParams.SwitchName = $nic.SwitchName
            } else {
                $availableSwitches = Invoke-Graceful { Get-VMSwitch } "获取交换机失败" "" @()
                if ($availableSwitches) {
                    $newNicParams.SwitchName = $availableSwitches[0].Name
                    Write-Host "源网络适配器未连接到交换机，尝试连接到 '$($availableSwitches[0].Name)'" -ForegroundColor Yellow
                } else {
                    Write-Host "源网络适配器未连接到交换机，且未找到可用交换机。新适配器将不连接。"
                }
            }
            $newAdapter = Invoke-Graceful { Add-VMNetworkAdapter @newNicParams -ErrorAction Stop } "添加网络适配器失败" "" $null
            if ($newAdapter) { Invoke-Graceful { Set-VMNetworkAdapter -VMName $newName -Name $newAdapter.Name -MacAddressSpoofing On -ErrorAction Stop } "配置网络适配器失败" -NoReturn }
        }
        foreach ($dvd in $sourceDVDs) {
            if ($dvd.Path) {
                Invoke-Graceful { Add-VMDvdDrive -VMName $newName -Path $dvd.Path -ControllerNumber $dvd.ControllerNumber -ControllerLocation $dvd.ControllerLocation -ErrorAction Stop } "添加DVD失败" -NoReturn
            } else {
                Invoke-Graceful { Add-VMDvdDrive -VMName $newName -ControllerNumber $dvd.ControllerNumber -ControllerLocation $dvd.ControllerLocation -ErrorAction Stop } "添加DVD失败" -NoReturn
            }
        }
        if ($sourceFirmware) {
            Write-Host "正在配置固件和启动顺序..."
            Invoke-Graceful { Set-VMFirmware -VMName $newName -EnableSecureBoot $sourceFirmware.SecureBoot -ErrorAction Stop } "配置固件失败" -NoReturn
            $newBootOrder = @()
            foreach ($bootDevice in $sourceFirmware.BootOrder) {
                if ($bootDevice.BootType -eq 'HardDisk') {
                    $newHDD = Invoke-Graceful { Get-VMHardDiskDrive -VMName $newName | Select-Object -First 1 } "获取新硬盘失败" "" $null
                    if ($newHDD) { $newBootOrder += $newHDD }
                } elseif ($bootDevice.BootType -eq 'DVD') {
                    $newDVD = Invoke-Graceful { Get-VMDvdDrive -VMName $newName | Where-Object {$_.Path -eq $bootDevice.Path} | Select-Object -First 1 } "获取新DVD失败" "" $null
                    if (-not $newDVD) { $newDVD = Invoke-Graceful { Get-VMDvdDrive -VMName $newName | Select-Object -First 1 } "获取新DVD失败" "" $null }
                    if ($newDVD) { $newBootOrder += $newDVD }
                } elseif ($bootDevice.BootType -eq 'NetworkAdapter') {
                    $newNetAdapter = Invoke-Graceful { Get-VMNetworkAdapter -VMName $newName | Select-Object -First 1 } "获取新网卡失败" "" $null
                    if ($newNetAdapter) { $newBootOrder += $newNetAdapter }
                }
            }
            $firstDvdDrive = Invoke-Graceful { Get-VMDvdDrive -VMName $newName | Select-Object -First 1 } "获取新DVD失败" "" $null
            if ($firstDvdDrive) {
                Write-Host "正在设置 DVD 驱动器为第一启动设备..."
                Invoke-Graceful { Set-VMFirmware -VMName $newName -FirstBootDevice $firstDvdDrive -ErrorAction Stop } "设置第一启动设备失败" -NoReturn
                Write-Host "已成功设置 DVD 驱动器为第一启动设备。"
            } elseif ($newBootOrder.Count -gt 0) {
                Invoke-Graceful { Set-VMFirmware -VMName $newName -BootOrder $newBootOrder -ErrorAction Stop } "设置启动顺序失败" -NoReturn
            } else {
                Write-Host "未能确定源启动顺序或设备，将使用默认启动顺序（通常是硬盘）。" -ForegroundColor Yellow
                $firstHdd = Invoke-Graceful { Get-VMHardDiskDrive -VMName $newName | Select-Object -First 1 } "获取新硬盘失败" "" $null
                if ($firstHdd) { Invoke-Graceful { Set-VMFirmware -VMName $newName -FirstBootDevice $firstHdd -ErrorAction SilentlyContinue } "设置第一启动设备失败" -NoReturn }
            }
        }
        Write-Host "虚拟机 '$newName' 已成功复制。" -ForegroundColor Green
    } "复制虚拟机失败" -NoReturn

    # 清理空目录
    Remove-EmptyDirectories -OperationType "复制"
}

function Import-VMCustom {
    # 获取配置的虚拟机路径
    # 注意：此功能使用 Register 模式导入，保持虚拟机原有路径结构和UUID
    # 不会将虚拟机复制到默认路径，而是就地注册现有的虚拟机文件
    # 由于导入前已通过UUID严格过滤重复虚拟机，无需额外的UUID冲突检测
    $vmBasePath = ""

    # 尝试从配置中获取虚拟机路径
    $vmBasePath = Get-EffectiveVMPath
    if (Test-Path $vmBasePath) {
        Write-Host "检测到默认虚拟机路径：$vmBasePath" -ForegroundColor Green
    } else {
        # 如果配置路径不存在，提示用户输入
        Write-Host "默认虚拟机路径不存在，请手动输入。" -ForegroundColor Yellow
        do {
            $vmBasePath = Read-Host "请输入虚拟机存储路径"
            if ([string]::IsNullOrWhiteSpace($vmBasePath) -or -not (Test-Path $vmBasePath)) {
                Write-Host "路径不能为空或不存在，请重新输入。" -ForegroundColor Red
            }
        } while ([string]::IsNullOrWhiteSpace($vmBasePath) -or -not (Test-Path $vmBasePath))
    }

    # 扫描虚拟机目录，查找所有.vmcx文件
    Write-Host "正在扫描虚拟机目录：$vmBasePath" -ForegroundColor Cyan
    $vmcxFiles = Invoke-Graceful {
        Get-ChildItem -Path $vmBasePath -Recurse -Filter "*.vmcx" -ErrorAction Stop
    } "扫描虚拟机目录失败" "请确保路径正确且有访问权限。" @()

    if (-not $vmcxFiles -or $vmcxFiles.Count -eq 0) {
        Write-Host "在指定路径中未找到任何虚拟机配置文件(.vmcx)。" -ForegroundColor Yellow
        return
    }

    # 获取所有现有虚拟机的UUID列表（用于重复检测）
    Write-Host "获取现有虚拟机UUID列表..." -ForegroundColor Cyan
    $existingVMUUIDs = @{}
    $existingVMs = Invoke-Graceful {
        Get-VM -ErrorAction Stop
    } "获取现有虚拟机列表失败" "" @()

    foreach ($existingVM in $existingVMs) {
        $existingVMUUIDs[$existingVM.Id.ToString()] = $existingVM.Name
    }

    Write-Host "当前系统中有 $($existingVMUUIDs.Count) 个虚拟机" -ForegroundColor Gray

    # 解析虚拟机信息，以虚拟机文件夹名称作为虚拟机名称
    $vmCandidates = @()
    foreach ($vmcxFile in $vmcxFiles) {
        # 获取虚拟机文件夹名称（.vmcx文件的父目录的父目录名称）
        # 路径结构：D:\Virtual Machine\Hyper-V\[VM_NAME]\Virtual Machines\[UUID].vmcx
        # 我们需要获取 [VM_NAME] 部分
        $vmcxDirectory = $vmcxFile.Directory.FullName  # D:\Virtual Machine\Hyper-V\[VM_NAME]\Virtual Machines
        $vmDirectory = Split-Path $vmcxDirectory -Parent  # D:\Virtual Machine\Hyper-V\[VM_NAME]
        $vmFolderName = Split-Path $vmDirectory -Leaf  # [VM_NAME]

        # 从.vmcx文件中读取虚拟机UUID
        Write-Host "  检查虚拟机：$vmFolderName" -ForegroundColor Gray
        $vmUUID = $null

        # 尝试从文件名中提取UUID（.vmcx文件名通常就是UUID）
        $vmcxFileName = [System.IO.Path]::GetFileNameWithoutExtension($vmcxFile.Name)
        if ($vmcxFileName -match '^[0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12}$') {
            $vmUUID = $vmcxFileName
        } else {
            # 如果文件名不是UUID格式，尝试从文件内容中读取
            $vmConfigContent = Invoke-Graceful {
                Get-Content -Path $vmcxFile.FullName -Raw -ErrorAction Stop
            } "读取虚拟机配置文件失败" "" ""

            if ($vmConfigContent) {
                # 尝试从XML内容中提取UUID
                if ($vmConfigContent -match '<property name="id" type="guid">([0-9A-Fa-f]{8}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{4}-[0-9A-Fa-f]{12})</property>') {
                    $vmUUID = $matches[1]
                }
            }
        }

        if (-not $vmUUID) {
            Write-Host "    ⚠️ 无法获取虚拟机UUID，跳过" -ForegroundColor Yellow
            continue
        }

        # 检查UUID是否已经存在（基于UUID的重复检测）
        if ($existingVMUUIDs.ContainsKey($vmUUID)) {
            $existingVMName = $existingVMUUIDs[$vmUUID]
            Write-Host "    ❌ 虚拟机UUID已存在（现有虚拟机：$existingVMName），跳过" -ForegroundColor Yellow
            continue
        }

        Write-Host "    ✅ 虚拟机UUID未冲突，可以导入" -ForegroundColor Green
        $vmCandidates += [pscustomobject]@{
            Name = $vmFolderName
            ConfigPath = $vmcxFile.FullName
            Directory = $vmDirectory
            UUID = $vmUUID
        }
    }

    if (-not $vmCandidates -or $vmCandidates.Count -eq 0) {
        Write-Host "未找到可导入的虚拟机（可能都已存在）。" -ForegroundColor Yellow
        return
    }

    # 显示可导入的虚拟机列表供用户选择
    Write-Host "`n找到以下可导入的虚拟机：" -ForegroundColor Green
    $selectedVMs = Interactive-Selection -Items $vmCandidates -DisplayItem {
        param($vm) "$($vm.Name) [UUID: $($vm.UUID.Substring(0,8))...] - $($vm.ConfigPath)"
    } -Header "请选择要导入的虚拟机（使用上/下箭头移动，空格键切换选择状态，回车确认；按 ESC 返回；无选择则返回）：" -Mode "Multi" -RefreshMode "Full"

    if (-not $selectedVMs -or $selectedVMs.Count -eq 0) {
        Write-Host "未选择任何虚拟机，返回上级。" -ForegroundColor Yellow
        return
    }

    # 导入选中的虚拟机
    foreach ($vm in $selectedVMs) {
        Write-Host "`n正在导入虚拟机：$($vm.Name)" -ForegroundColor Cyan

        # 进行兼容性检查（使用Register模式保持原有路径和ID）
        Write-Host "进行兼容性检查..." -ForegroundColor Yellow
        $compatibilityReport = Invoke-Graceful {
            Compare-VM -Path $vm.ConfigPath -Register -ErrorAction Stop
        } "检查虚拟机兼容性失败" "请检查虚拟机配置文件是否完整。" $null

        if (-not $compatibilityReport) {
            Write-Host "虚拟机 '$($vm.Name)' 兼容性检查失败，跳过导入。" -ForegroundColor Red
            continue
        }

        # 检查是否有不兼容问题并自动修复
        if ($compatibilityReport.Incompatibilities -and $compatibilityReport.Incompatibilities.Count -gt 0) {
            Write-Host "发现兼容性问题：" -ForegroundColor Yellow
            foreach ($incompatibility in $compatibilityReport.Incompatibilities) {
                Write-Host "  - $($incompatibility.Message)" -ForegroundColor Red
            }

            # 尝试自动修复一些常见问题
            Write-Host "尝试自动修复兼容性问题..." -ForegroundColor Yellow

            # 获取可用的虚拟交换机
            $availableSwitches = Invoke-Graceful {
                Get-VMSwitch -ErrorAction Stop
            } "获取虚拟交换机列表失败" "" @()

            foreach ($incompatibility in $compatibilityReport.Incompatibilities) {
                if ($incompatibility.Source -is [Microsoft.HyperV.PowerShell.VMNetworkAdapter]) {
                    Write-Host "  修复网络适配器连接问题..." -ForegroundColor Cyan

                    if ($availableSwitches -and $availableSwitches.Count -gt 0) {
                        # 智能选择交换机：优先使用 Default Switch
                        $preferredSwitches = @()
                        $defaultSwitch = $availableSwitches | Where-Object { $_.Name -eq "Default Switch" } | Select-Object -First 1
                        if ($defaultSwitch) {
                            $preferredSwitches += $defaultSwitch
                        }
                        # 添加其他交换机作为备选
                        $otherSwitches = $availableSwitches | Where-Object { $_.Name -ne "Default Switch" }
                        $preferredSwitches += $otherSwitches

                        $connected = $false
                        foreach ($switch in $preferredSwitches) {
                            Write-Host "    尝试连接到交换机 '$($switch.Name)'" -ForegroundColor Gray

                            $connectResult = Invoke-Graceful {
                                $incompatibility.Source | Connect-VMNetworkAdapter -SwitchName $switch.Name -ErrorAction Stop
                                $true
                            } "" "" $false

                            if ($connectResult) {
                                Write-Host "    ✓ 已连接到交换机 '$($switch.Name)'" -ForegroundColor Green
                                $connected = $true
                                break
                            } else {
                                Write-Host "    ✗ 连接到 '$($switch.Name)' 失败" -ForegroundColor Yellow
                            }
                        }

                        if (-not $connected) {
                            Write-Host "    所有交换机连接失败，断开网络适配器" -ForegroundColor Yellow
                            $incompatibility.Source | Disconnect-VMNetworkAdapter -ErrorAction SilentlyContinue
                        }
                    } else {
                        # 没有可用交换机，断开连接
                        Write-Host "    未找到可用交换机，断开网络适配器连接" -ForegroundColor Yellow
                        $incompatibility.Source | Disconnect-VMNetworkAdapter -ErrorAction SilentlyContinue
                    }
                } elseif ($incompatibility.Source -is [Microsoft.HyperV.PowerShell.HardDiskDrive]) {
                    # 处理磁盘文件不存在的问题
                    Write-Host "  修复磁盘文件问题..." -ForegroundColor Cyan
                    $diskPath = $incompatibility.Source.Path
                    Write-Host "    磁盘文件不存在：$diskPath" -ForegroundColor Yellow
                    Write-Host "    移除不存在的磁盘驱动器" -ForegroundColor Gray

                    $removeResult = Invoke-Graceful {
                        $incompatibility.Source | Remove-VMHardDiskDrive -ErrorAction Stop
                        $true
                    } "" "" $false

                    if ($removeResult) {
                        Write-Host "    ✓ 已移除不存在的磁盘驱动器" -ForegroundColor Green
                    } else {
                        Write-Host "    ✗ 移除磁盘驱动器失败" -ForegroundColor Red
                    }
                }
            }

            # 重新检查兼容性
            $compatibilityReport = Invoke-Graceful {
                Compare-VM -CompatibilityReport $compatibilityReport -ErrorAction Stop
            } "重新检查兼容性失败" "" $null
        }

        # 使用兼容性报告导入虚拟机
        Write-Host "导入虚拟机（保持原有ID和路径）..." -ForegroundColor Cyan
        $result = Invoke-Graceful {
            $importedVM = Import-VM -CompatibilityReport $compatibilityReport -ErrorAction Stop
            $importedVM
        } "导入虚拟机 '$($vm.Name)' 失败" "请检查虚拟机配置文件是否完整，以及是否有足够的权限。" $null

        if ($result) {
            Write-Host "虚拟机 '$($vm.Name)' 导入成功（保持原有ID和路径）。" -ForegroundColor Green

            # 显示虚拟机的实际存储路径
            $importedVMName = $result.Name
            $vmInfo = Get-VM -Name $importedVMName
            Write-Host "虚拟机配置文件路径：$($vmInfo.ConfigurationLocation)" -ForegroundColor Gray

            Write-Host "检查虚拟机磁盘配置..." -ForegroundColor Cyan
            $disks = Invoke-Graceful {
                Get-VMHardDiskDrive -VMName $importedVMName -ErrorAction Stop
            } "获取虚拟机磁盘信息失败" "" @()

            if ($disks -and $disks.Count -gt 0) {
                Write-Host "虚拟机 '$importedVMName' 的磁盘文件：" -ForegroundColor Green
                foreach ($disk in $disks) {
                    if (Test-Path $disk.Path) {
                        Write-Host "  ✓ $($disk.Path)" -ForegroundColor Green
                    } else {
                        Write-Host "  ✗ $($disk.Path) (文件不存在)" -ForegroundColor Red
                    }
                }
            } else {
                Write-Host "虚拟机 '$importedVMName' 没有配置磁盘。" -ForegroundColor Yellow
            }
        } else {
            Write-Host "虚拟机 '$($vm.Name)' 导入失败。" -ForegroundColor Red
            Write-Host "建议检查：" -ForegroundColor Yellow
            Write-Host "  1. 虚拟机配置文件是否完整" -ForegroundColor Gray
            Write-Host "  2. 磁盘文件是否存在" -ForegroundColor Gray
            Write-Host "  3. 是否有管理员权限" -ForegroundColor Gray
            Write-Host "  4. 网络配置是否兼容" -ForegroundColor Gray
        }
    }

    # 清理空目录
    Remove-EmptyDirectories -OperationType "导入"
    Write-Host "按任意键返回上级菜单..." -ForegroundColor Yellow
    [void][Console]::ReadKey($true)
}

function Export-VMCustom {
    # 获取所有虚拟机列表
    $vms = Invoke-Graceful { Get-VM | Sort-Object Name } "获取虚拟机列表失败" "" @()
    if (-not $vms -or $vms.Count -eq 0) {
        Write-Host "当前未检测到任何虚拟机，请先创建一个虚拟机。" -ForegroundColor Yellow
        return
    }

    # 显示虚拟机状态信息
    Write-Host "`n当前虚拟机状态：" -ForegroundColor Cyan
    Write-Host "══════════════════════════════════════════════════" -ForegroundColor DarkCyan
    foreach ($vm in $vms) {
        $stateColor = switch ($vm.State) {
            'Running' { 'Green' }
            'Off' { 'Gray' }
            'Saved' { 'Yellow' }
            default { 'White' }
        }
        Write-Host "  $($vm.Name) - " -NoNewline
        Write-Host $vm.State -ForegroundColor $stateColor
    }
    Write-Host

    # 选择要导出的虚拟机（支持多选）
    $selectedVMs = Interactive-Selection -Items $vms -DisplayItem {
        param($vm)
        $stateIcon = switch ($vm.State) {
            'Running' { '●' }
            'Off' { '○' }
            'Saved' { '◐' }
            default { '?' }
        }
        "$stateIcon $($vm.Name) [$($vm.State)]"
    } -Header "请选择要导出的虚拟机（使用上/下箭头移动，空格键切换选择状态，回车确认；按 ESC 返回；无选择则返回）：" -Mode "Multi" -RefreshMode "Full"

    if (-not $selectedVMs -or $selectedVMs.Count -eq 0) {
        Write-Host "未选择任何虚拟机，返回上级。" -ForegroundColor Yellow
        return
    }

    # 输入导出目标路径
    Write-Host "`n请输入导出目标文件夹路径：" -ForegroundColor Cyan
    Write-Host "提示：" -ForegroundColor Gray
    Write-Host "  - 路径示例：D:\VM_Exports" -ForegroundColor Gray
    Write-Host "  - 如果目录不存在，将自动创建" -ForegroundColor Gray
    Write-Host "  - 每个虚拟机将导出到单独的子文件夹" -ForegroundColor Gray
    Write-Host

    $exportPath = Read-Host "导出路径"
    if ([string]::IsNullOrWhiteSpace($exportPath)) {
        Write-Host "未输入导出路径，操作取消。" -ForegroundColor Yellow
        return
    }

    # 验证和创建导出路径
    $result = Invoke-Graceful {
        if (-not (Test-Path $exportPath)) {
            New-Item -Path $exportPath -ItemType Directory -Force -ErrorAction Stop | Out-Null
            Write-Host "已创建导出目录：$exportPath" -ForegroundColor Green
        }

        # 验证路径是否可写
        $testFile = Join-Path $exportPath "test_write_$(Get-Date -Format 'yyyyMMddHHmmss').tmp"
        New-Item -Path $testFile -ItemType File -Force -ErrorAction Stop | Out-Null
        Remove-Item -Path $testFile -Force -ErrorAction Stop
        $true
    } "无法创建或访问导出路径" "请检查路径是否正确，以及是否有足够的权限和磁盘空间。" $false

    if (-not $result) { return }

    # 询问运行中虚拟机的处理方式
    $runningVMs = $selectedVMs | Where-Object { $_.State -eq 'Running' }
    $captureMode = 'CaptureSavedState'  # 默认值

    if ($runningVMs -and $runningVMs.Count -gt 0) {
        Write-Host "`n检测到 $($runningVMs.Count) 个运行中的虚拟机：" -ForegroundColor Yellow
        foreach ($vm in $runningVMs) {
            Write-Host "  - $($vm.Name)" -ForegroundColor Cyan
        }

        Write-Host "`n请选择运行中虚拟机的导出方式：" -ForegroundColor Cyan
        Write-Host "1. 包含内存状态（推荐）- 保存虚拟机当前运行状态" -ForegroundColor Green
        Write-Host "2. 生产检查点 - 使用 VSS 技术创建一致性快照" -ForegroundColor Yellow
        Write-Host "3. 崩溃一致性 - 不处理虚拟机状态，可能导致数据不一致" -ForegroundColor Red

        do {
            $choice = Read-HostWithDefault "请选择 (1-3) [默认: 1]" "1" ([string])
        } while ($choice -notmatch '^[1-3]$')

        $captureMode = switch ($choice) {
            '1' { 'CaptureSavedState' }
            '2' { 'CaptureDataConsistentState' }
            '3' { 'CaptureCrashConsistentState' }
        }

        $modeDescription = switch ($captureMode) {
            'CaptureSavedState' { '包含内存状态' }
            'CaptureDataConsistentState' { '生产检查点' }
            'CaptureCrashConsistentState' { '崩溃一致性' }
        }
        Write-Host "已选择：$modeDescription" -ForegroundColor Green
    }

    # 开始导出虚拟机
    Write-Host "`n开始导出虚拟机..." -ForegroundColor Cyan
    Write-Host "══════════════════════════════════════════════════" -ForegroundColor DarkCyan

    $successCount = 0; $failCount = 0; $exportResults = @()

    foreach ($vm in $selectedVMs) {
        Write-Host "`n正在导出虚拟机：$($vm.Name)" -ForegroundColor Yellow
        Write-Host "状态：$($vm.State)" -ForegroundColor Gray

        $vmExportPath = Join-Path $exportPath $vm.Name

        $result = Invoke-Graceful {
            if ($vm.State -eq 'Running') {
                Export-VM -Name $vm.Name -Path $exportPath -CaptureLiveState $captureMode -ErrorAction Stop
            } else {
                Export-VM -Name $vm.Name -Path $exportPath -ErrorAction Stop
            }
            $true
        } "导出虚拟机 '$($vm.Name)' 失败" "请检查虚拟机状态、磁盘空间和权限。" $false

        if ($result) {
            Write-Host "✓ 虚拟机 '$($vm.Name)' 导出成功" -ForegroundColor Green
            $successCount++
            $exportResults += [PSCustomObject]@{
                Name = $vm.Name
                Status = "成功"
                Path = $vmExportPath
            }
        } else {
            Write-Host "✗ 虚拟机 '$($vm.Name)' 导出失败" -ForegroundColor Red
            $failCount++
            $exportResults += [PSCustomObject]@{
                Name = $vm.Name
                Status = "失败"
                Path = ""
            }
        }
    }

    # 显示导出结果摘要
    Write-Host "`n导出操作完成" -ForegroundColor Cyan
    Write-Host "══════════════════════════════════════════════════" -ForegroundColor DarkCyan
    Write-Host "成功：$successCount 个" -ForegroundColor Green
    Write-Host "失败：$failCount 个" -ForegroundColor Red
    Write-Host "导出路径：$exportPath" -ForegroundColor Gray

    if ($exportResults.Count -gt 0) {
        Write-Host "`n详细结果：" -ForegroundColor Yellow
        $exportResults | Format-Table -AutoSize
    }

    Write-Host "`n按任意键返回上级菜单..." -ForegroundColor Yellow
    [void][Console]::ReadKey($true)
}

function Add-VirtualDisk {
    $formats = @("vhdx", "vhd")
    $header = "请选择磁盘格式`n（使用上/下箭头移动，按空格键选择，回车确认, 未选择则返回）"
    $selFormatResult = Interactive-Selection -Items $formats -DisplayItem { param($fmt) "$fmt" } -Header $header -Mode "Single"
    if ($selFormatResult.IsBack -or -not $selFormatResult.Result) {
        Write-Host "未选择磁盘格式或已取消操作，返回上级菜单。"
        return
    }
    $diskFormat = $selFormatResult.Result

    $diskSizeGB = Read-HostWithDefault "请输入虚拟磁盘大小（单位 GB） [默认: 120]" 120 ([double])
    if (-not $diskSizeGB) {
        Write-Host "无效大小输入，操作取消。"
        return
    }
    $sizeBytes = [math]::Round($diskSizeGB * 1GB)
    $diskNameInput = Read-Host "请输入虚拟磁盘名称 (回车使用默认名称)"; $timestamp = Get-Date -Format "yyyyMMddHHmmss"
    if ([string]::IsNullOrWhiteSpace($diskNameInput)) {
        $diskFileName = "NewDisk_$timestamp.$diskFormat"
    } else {
        if ($diskNameInput -notmatch "\\.(vhdx|vhd)$") {
            $diskFileName = "$diskNameInput.$diskFormat"
        } else {
            $diskFileName = $diskNameInput
        }
    }
    $defaultPath = Get-EffectiveVHDPath; $diskPath = Join-Path -Path $defaultPath -ChildPath $diskFileName
    
    $result = Invoke-Graceful { 
        New-VHD -Path $diskPath -SizeBytes $sizeBytes -Dynamic -ErrorAction Stop | Out-Null
        Write-Host "虚拟磁盘 '$diskPath' 创建成功（大小：$diskSizeGB GB）。"
        $true
    } "创建虚拟磁盘失败" "" $false
    
    if (-not $result) { return }

    # 获取可用的虚拟机列表
    $vms = Get-VM | Sort-Object Name
    if ($vms -and $vms.Count -gt 0) {
        Write-Host "`n选择要挂载磁盘的虚拟机（直接回车跳过挂载）："
        $vmResult = Interactive-Selection -Items $vms -DisplayItem { param($vm) "$($vm.Name) - $($vm.State)" } -Header "请选择虚拟机`n（使用上/下箭头移动，按空格键选择，回车确认, 未选择则返回）" -Mode "Single"
        if (-not $vmResult.IsBack -and $vmResult.Result) {
            $vm = $vmResult.Result
            $result = Invoke-Graceful { 
                Add-VMHardDiskDrive -VMName $vm.Name -Path $diskPath -ErrorAction Stop
                Write-Host "虚拟磁盘已成功挂载到虚拟机 '$($vm.Name)'。"
                $true
            } "挂载虚拟磁盘失败" "" $false
        } else {
            Write-Host "未选择虚拟机或已取消操作，跳过挂载。"
        }
    } else {
        Write-Host "当前没有可用的虚拟机。"
    }
}

function Mount-VirtualDisk {
    # 获取配置的存储路径
    $vhdPath = Get-EffectiveVHDPath

    # 获取所有虚拟磁盘文件并去重
    $diskFiles = @()
    $diskFiles += Get-ChildItem -Path $vhdPath -Filter *.vhdx -ErrorAction SilentlyContinue
    $diskFiles += Get-ChildItem -Path $vhdPath -Filter *.vhd -ErrorAction SilentlyContinue
    
    # 使用字典进行去重，以文件名为键
    $uniqueDisks = @{}
    foreach ($disk in $diskFiles) {
        if (-not $uniqueDisks.ContainsKey($disk.Name)) {
            $uniqueDisks[$disk.Name] = $disk
        }
    }
    $diskFiles = $uniqueDisks.Values

    if (-not $diskFiles -or $diskFiles.Count -eq 0) {
        Write-Host "未找到任何虚拟磁盘文件。"
        return
    }

    # 获取所有虚拟机的磁盘信息
    $vmDisks = @{}
    $vms = Get-VM
    foreach ($vm in $vms) {
        $disks = Get-VMHardDiskDrive -VMName $vm.Name -ErrorAction SilentlyContinue
        foreach ($disk in $disks) {
            $vmDisks[$disk.Path] = $vm.Name
        }
    }

    # 筛选出未挂载的磁盘
    $unmountedDisks = $diskFiles | Where-Object { -not $vmDisks.ContainsKey($_.FullName) } | Sort-Object Name

    if (-not $unmountedDisks -or $unmountedDisks.Count -eq 0) {
        Write-Host "`n未找到任何未挂载的磁盘。"
        return
    }

    # 使用统一的交互选择界面
    $customHeader = {
        Write-Host "`n可用磁盘列表" -ForegroundColor Yellow
        Write-Host "══════════════════════════════════════════════════" -ForegroundColor DarkCyan
        Write-Host
    }

    $diskResult = Interactive-Selection -Items $unmountedDisks -DisplayItem {
        param($disk)
        $sizeGB = [math]::Round($disk.Length / 1GB, 2)
        "$($disk.Name) ($sizeGB GB)"
    } -Header "请选择要挂载的磁盘（使用上/下箭头移动，空格键选择，回车确认；按 ESC 返回；无选择则返回）：" -Mode "Single" -CustomHeader $customHeader

    if ($diskResult.IsBack -or -not $diskResult.Result) {
        Write-Host "`n未选择任何磁盘，返回上级。"
        return
    }

    $selectedDisk = $diskResult.Result

    # 选择要挂载到的虚拟机
    $targetVMResult = Interactive-Selection -Items $vms -DisplayItem { param($vm) "$($vm.Name) - $($vm.State)" } -Header "请选择虚拟机`n（使用上/下箭头移动，按空格键选择，回车确认, 未选择则返回）" -Mode "Single"
    if ($targetVMResult.IsBack -or -not $targetVMResult.Result) {
        Write-Host "`n未选择虚拟机或已取消操作。"
        return
    }
    $targetVM = $targetVMResult.Result

    $result = Invoke-Graceful { 
        Add-VMHardDiskDrive -VMName $targetVM.Name -Path $selectedDisk.FullName -ErrorAction Stop
        Write-Host "`n已成功将磁盘 '$($selectedDisk.Name)' 挂载到虚拟机 '$($targetVM.Name)'。" -ForegroundColor Green
        $true
    } "挂载磁盘失败" "" $false
}

function Unmount-VirtualDisk {
    # 获取所有虚拟机
    $vms = Get-VM | Sort-Object Name
    if (-not $vms -or $vms.Count -eq 0) {
        Write-Host "未找到任何虚拟机。"
        return
    }

    # 选择虚拟机
    $vmResult = Interactive-Selection -Items $vms -DisplayItem { param($vm) "$($vm.Name) - $($vm.State)" } -Header "请选择虚拟机`n（使用上/下箭头移动，按空格键选择，回车确认, 未选择则返回）" -Mode "Single"
    if ($vmResult.IsBack -or -not $vmResult.Result) {
        Write-Host "`n未选择虚拟机或已取消操作，返回上级。"
        return
    }
    $vm = $vmResult.Result

    # 获取选中虚拟机的所有磁盘
    $disks = Get-VMHardDiskDrive -VMName $vm.Name
    if (-not $disks -or $disks.Count -eq 0) {
        Write-Host "`n虚拟机 '$($vm.Name)' 没有挂载任何磁盘。"
        return
    }

    # 使用统一的交互选择界面
    $customHeader = {
        Write-Host "`n选择要移除挂载的磁盘" -ForegroundColor Yellow
        Write-Host "══════════════════════════════════════════════════" -ForegroundColor DarkCyan
        Write-Host
    }

    $selectedDisks = Interactive-Selection -Items $disks -DisplayItem {
        param($disk)
        $diskPath = $disk.Path
        $diskName = Split-Path $diskPath -Leaf
        $diskSize = if (Test-Path $diskPath) {
            $size = (Get-Item $diskPath).Length
            "$([math]::Round($size/1GB, 2)) GB"
        } else {
            "未知大小"
        }
        "$diskName ($diskSize)"
    } -Header "请选择要移除挂载的磁盘（使用上/下箭头移动，空格键切换选择状态，回车确认；按 ESC 返回；无选择则返回）：" -Mode "Multi" -CustomHeader $customHeader

    if (-not $selectedDisks -or $selectedDisks.Count -eq 0) {
        Write-Host "`n未选择任何磁盘，返回上级。"
        return
    }

    # 如果虚拟机正在运行，提示用户
    if ($vm.State -eq 'Running') {
        Write-Host "`n警告：虚拟机正在运行，移除磁盘可能会导致数据丢失或系统不稳定。" -ForegroundColor Red
        if (-not (Confirm-YesNo "是否继续?" $false)) {
            Write-Host "操作已取消。"
            return
        }
    }

    # 移除选中的磁盘挂载
    foreach ($disk in $selectedDisks) {
        Invoke-Graceful {
            Remove-VMHardDiskDrive -VMName $vm.Name -ControllerType $disk.ControllerType -ControllerNumber $disk.ControllerNumber -ControllerLocation $disk.ControllerLocation -ErrorAction Stop
            Write-Host "`n已成功移除磁盘挂载：$(Split-Path $disk.Path -Leaf)" -ForegroundColor Green
        } "移除磁盘挂载失败：$(Split-Path $disk.Path -Leaf)" -NoReturn
    }
}

function Remove-VirtualDisks {
    # 获取配置的存储路径
    $vhdPath = Get-EffectiveVHDPath

    # 获取所有虚拟磁盘文件并去重
    $diskFiles = @()
    $diskFiles += Get-ChildItem -Path $vhdPath -Filter *.vhdx -ErrorAction SilentlyContinue
    $diskFiles += Get-ChildItem -Path $vhdPath -Filter *.vhd -ErrorAction SilentlyContinue
    
    # 使用字典进行去重，以文件名为键
    $uniqueDisks = @{}
    foreach ($disk in $diskFiles) {
        if (-not $uniqueDisks.ContainsKey($disk.Name)) {
            $uniqueDisks[$disk.Name] = $disk
        }
    }
    $diskFiles = $uniqueDisks.Values

    if (-not $diskFiles -or $diskFiles.Count -eq 0) {
        Write-Host "未找到任何虚拟磁盘文件。"
        return
    }

    # 获取所有虚拟机的磁盘信息和运行状态
    $vmDisks = @{}; $vmStates = @{}; $vms = Get-VM
    foreach ($vm in $vms) {
        $disks = Get-VMHardDiskDrive -VMName $vm.Name -ErrorAction SilentlyContinue
        foreach ($disk in $disks) {
            $vmDisks[$disk.Path] = $vm.Name
            $vmStates[$disk.Path] = $vm.State
        }
    }

    # 对磁盘进行排序：已挂载的在前，未挂载的在后
    # 已挂载的按照虚拟机名称分组，同组内按磁盘名称排序
    # 未挂载的按磁盘名称排序
    $sortedDiskFiles = $diskFiles | Sort-Object `
        { # 第一级排序：已挂载在前，未挂载在后
            if ($vmDisks.ContainsKey($_.FullName)) { 0 } else { 1 }
        }, `
        { # 第二级排序：按挂载的虚拟机名称
            if ($vmDisks.ContainsKey($_.FullName)) {
                $vmDisks[$_.FullName]
            } else {
                [string]::Empty
            }
        }, `
        { # 第三级排序：按磁盘名称
            $_.Name
        }

    function Show-Header {
        Write-Host
        Write-Host "可用磁盘列表" -ForegroundColor Yellow
        Write-Host "══════════════════════════════════════════════════" -ForegroundColor DarkCyan
        Write-Host

        # 使用新的描述格式
        Write-Host "[ " -NoNewline -ForegroundColor DarkCyan

        # 显示挂载状态
        Write-Host "已挂载" -NoNewline -ForegroundColor Red
        Write-Host "/" -NoNewline -ForegroundColor DarkCyan
        Write-Host "未挂载" -NoNewline -ForegroundColor Green

        Write-Host " | " -NoNewline -ForegroundColor DarkCyan

        # 显示磁盘占用
        Write-Host "磁盘占用" -NoNewline -ForegroundColor DarkYellow

        Write-Host " | " -NoNewline -ForegroundColor DarkCyan

        # 显示挂载位置（使用与列表一致的颜色）
        Write-Host "★" -NoNewline -ForegroundColor Magenta
        Write-Host " 挂载位置" -NoNewline -ForegroundColor Cyan

        Write-Host " ]" -ForegroundColor DarkCyan
        Write-Host
    }

    $count = $sortedDiskFiles.Count; $selected = @(); for ($i = 0; $i -lt $count; $i++) { $selected += $false }; $currentIndex = 0

    while ($true) {
        Clear-Host
        Show-Header

        for ($i = 0; $i -lt $count; $i++) {
            $disk = $sortedDiskFiles[$i]
            $isMounted = $vmDisks.ContainsKey($disk.FullName)
            $sizeGB = [math]::Round($disk.Length / 1GB, 2)
            $marker = if ($selected[$i]) { "[*]" } else { "[ ]" }

            # 显示选择框和箭头
            if ($i -eq $currentIndex) {
                Write-Host "-> " -NoNewline -ForegroundColor Yellow
            } else {
                Write-Host "   " -NoNewline
            }
            Write-Host $marker -NoNewline
            Write-Host " " -NoNewline

            # 显示磁盘名称（红色或绿色）
            Write-Host $disk.Name -NoNewline -ForegroundColor $(if ($isMounted) { "Red" } else { "Green" })

            # 显示大小（暗黄色）
            Write-Host " ($sizeGB GB)" -NoNewline -ForegroundColor DarkYellow

            # 显示虚拟机名称（如果有）
            if ($isMounted) {
                Write-Host " " -NoNewline
                Write-Host "★" -NoNewline -ForegroundColor Magenta
                Write-Host " " -NoNewline
                Write-Host $vmDisks[$disk.FullName] -ForegroundColor Cyan
            } else {
                Write-Host
            }
        }

        $key = [Console]::ReadKey($true)
        switch ($key.Key) {
            "UpArrow"   {
                $currentIndex--
                if ($currentIndex -lt 0) { $currentIndex = $count - 1 }
            }
            "DownArrow" {
                $currentIndex = ($currentIndex + 1) % $count
            }
            "Spacebar"  {
                $selected[$currentIndex] = -not $selected[$currentIndex]
            }
            "Enter"     { break }
        }
        if ($key.Key -eq "Enter") { break }
    }

    $selectedUserDisks = @()
    for ($i = 0; $i -lt $count; $i++) {
        if ($selected[$i]) {
            $selectedUserDisks += $sortedDiskFiles[$i]
        }
    }

    if (-not $selectedUserDisks -or $selectedUserDisks.Count -eq 0) {
        Write-Host "`n未选择任何磁盘，返回上级。"
        return
    }

    foreach ($disk in $selectedUserDisks) {
        if ($vmDisks.ContainsKey($disk.FullName)) {
            $vmName = $vmDisks[$disk.FullName]
            $vmState = $vmStates[$disk.FullName]
            
            if ($vmState -eq 'Running') {
                Write-Host "`n磁盘 '$($disk.Name)' 正在被运行中的虚拟机 '$vmName' 使用，无法删除。" -ForegroundColor Red
                continue
            }
            
            # 如果虚拟机未运行，先移除磁盘挂载，然后删除文件
            $result = Invoke-Graceful {
                $vm = Get-VM -Name $vmName
                $vmDisk = Get-VMHardDiskDrive -VMName $vmName | Where-Object { $_.Path -eq $disk.FullName }
                if ($vmDisk) {
                    Remove-VMHardDiskDrive -VMHardDiskDrive $vmDisk -ErrorAction Stop
                    Write-Host "`n已从虚拟机 '$vmName' 移除磁盘挂载。" -ForegroundColor Yellow
                }
                Remove-Item -Path $disk.FullName -Force -ErrorAction Stop
                Write-Host "已成功删除磁盘：$($disk.Name)" -ForegroundColor Green
                $true
            } "删除磁盘 '$($disk.Name)' 失败" "" $false
        } else {
            $result = Invoke-Graceful {
                Remove-Item -Path $disk.FullName -Force -ErrorAction Stop
                Write-Host "`n已成功删除磁盘：$($disk.Name)" -ForegroundColor Green
                $true
            } "删除磁盘 '$($disk.Name)' 失败" "" $false
        }
    }
}

function Mount-ISOImage {
    param(
        [Parameter(Mandatory=$false)]
        $TargetVM
    )
    if (-not $TargetVM) {
        $vms = Get-VM | Sort-Object Name
        if (-not $vms -or $vms.Count -eq 0) {
            Write-Host "当前未检测到任何虚拟机。"
            return
        }
        $vmResult = Interactive-Selection -Items $vms -DisplayItem { param($vm) "$($vm.Name) - $($vm.State)" } -Header "请选择虚拟机`n（使用上/下箭头移动，按空格键选择，回车确认, 未选择则返回）" -Mode "Single"
        if ($vmResult.IsBack -or -not $vmResult.Result) {
            Write-Host "未选择虚拟机，挂载操作已取消。"
            return
        }
        $TargetVM = $vmResult.Result
    }
    Mount-IsoCommon -TargetVM $TargetVM
}

function Unmount-ISOImage {
    # 获取所有虚拟机
    $vms = Get-VM | Sort-Object Name
    if (-not $vms -or $vms.Count -eq 0) {
        Write-Host "当前未检测到任何虚拟机，请先创建一个虚拟机。" -ForegroundColor Yellow
        return
    }

    # 选择虚拟机
    $vmResult = Interactive-Selection -Items $vms -DisplayItem { param($vm) "$($vm.Name) [$($vm.State)]" } -Header "请选择要移除 ISO 挂载的虚拟机`n（使用上/下箭头移动，按空格键选择，回车确认, 无选择则返回）" -Mode "Single"
    if (-not $vmResult -or $vmResult.IsBack) {
        Write-Host "未选择虚拟机，操作已取消。"
        return
    }
    $vm = $vmResult.Result

    # 获取虚拟机的DVD驱动器
    $dvdDrives = Invoke-Graceful {
        Get-VMDvdDrive -VMName $vm.Name -ErrorAction Stop
    } "获取DVD驱动器失败" "" @()

    if (-not $dvdDrives -or $dvdDrives.Count -eq 0) {
        Write-Host "虚拟机 '$($vm.Name)' 没有DVD驱动器。" -ForegroundColor Yellow
        return
    }

    # 筛选出已挂载ISO的驱动器
    $mountedDrives = $dvdDrives | Where-Object { $_.Path -and $_.Path.Trim() -ne "" }
    if (-not $mountedDrives -or $mountedDrives.Count -eq 0) {
        Write-Host "虚拟机 '$($vm.Name)' 没有挂载任何ISO镜像。" -ForegroundColor Yellow
        return
    }

    # 选择要移除的ISO
    $driveResult = Interactive-Selection -Items $mountedDrives -DisplayItem { param($drive) "控制器 $($drive.ControllerNumber):$($drive.ControllerLocation) - $(Split-Path $drive.Path -Leaf)" } -Header "请选择要移除的ISO镜像`n（使用上/下箭头移动，按空格键选择，回车确认, 无选择则返回）" -Mode "Multi" -RefreshMode "Full"
    if (-not $driveResult -or $driveResult.Count -eq 0) {
        Write-Host "未选择ISO镜像，操作已取消。"
        return
    }

    # 移除选中的ISO挂载和对应的DVD驱动器
    $removedCount = 0
    foreach ($drive in $driveResult) {
        $isoName = Split-Path $drive.Path -Leaf
        $result = Invoke-Graceful {
            Remove-VMDvdDrive -VMName $vm.Name -ControllerNumber $drive.ControllerNumber -ControllerLocation $drive.ControllerLocation -ErrorAction Stop
            $true
        } "移除DVD驱动器失败" "" $false

        if ($result) {
            Write-Host "已移除ISO镜像和DVD驱动器：$isoName" -ForegroundColor Cyan
            $removedCount++
        }
    }

    if ($removedCount -eq 0) {
        Write-Host "没有成功移除任何ISO镜像和DVD驱动器。" -ForegroundColor Red
    } else {
        Write-Host "`n成功移除了 $removedCount 个ISO镜像和对应的DVD驱动器。" -ForegroundColor Green
    }
}

function Manage-ISO {
    $options = @(
        [pscustomobject]@{ Label = "挂载"; Action = { Mount-ISOImage } },
        [pscustomobject]@{ Label = "移除"; Action = { Unmount-ISOImage } }
    )

    $selection = Interactive-Selection -Items $options -DisplayItem { param($item) $item.Label } -Header "请选择ISO镜像操作（使用上下箭头移动，空格键选择，回车确认；按 ESC 键返回主菜单）：" -Mode "Single"

    if ($selection.IsBack) {
        return
    } elseif ($selection.Result) {
        & $selection.Result.Action
    }
}

function Set-BaseDirs {
    $config = Get-HyperVConfig; $vmHost = Get-VMHost
    
    while ($true) {
        # 构建配置信息显示
        $extraInfo = @"
当前配置：
══════════════════════════════════════════════════
虚拟磁盘存放路径: $($vmHost.VirtualHardDiskPath)
虚拟机存放路径:   $($vmHost.VirtualMachinePath)
ISO 镜像目录:     $($config.ISOPath -join ', ')
══════════════════════════════════════════════════
"@

        # 使用交互式选择
        $options = @(
            [pscustomobject]@{ 
                Label = "修改虚拟磁盘存放路径"
                Action = { 
                    $newVHD = Read-Host "请输入新的虚拟磁盘存放位置"
    if (-not [string]::IsNullOrWhiteSpace($newVHD)) {
        Ensure-Directory -Path $newVHD -Description "虚拟磁盘目录"
        $result = Invoke-Graceful {
            Set-VMHost -VirtualHardDiskPath $newVHD -ErrorAction Stop
            $config.VHDPath = $newVHD
            Save-HyperVConfig $config
            Write-Host "虚拟磁盘存放路径已更新。" -ForegroundColor Green
            Start-Sleep -Seconds 2
            $true
        } "更新虚拟磁盘存放路径失败" "" $false
        if (-not $result) { Start-Sleep -Seconds 2 }
    }
                }
            },
            [pscustomobject]@{ 
                Label = "修改虚拟机存放路径"
                Action = {
                    $newVM = Read-Host "请输入新的虚拟机存放位置"
    if (-not [string]::IsNullOrWhiteSpace($newVM)) {
        Ensure-Directory -Path $newVM -Description "虚拟机目录"
        $result = Invoke-Graceful {
            Set-VMHost -VirtualMachinePath $newVM -ErrorAction Stop
            $config.VMPath = $newVM
            Save-HyperVConfig $config
            Write-Host "虚拟机存放路径已更新。" -ForegroundColor Green
            Start-Sleep -Seconds 2
            $true
        } "更新虚拟机存放路径失败" "" $false
        if (-not $result) { Start-Sleep -Seconds 2 }
    }
                }
            },
            [pscustomobject]@{ 
                Label = "管理 ISO 镜像目录"
                Action = {
                    while ($true) {
                        Write-Host "`n当前 ISO 镜像目录：" -ForegroundColor Yellow
                        if ($config.ISOPath -and $config.ISOPath.Count -gt 0) {
                            $isoOptions = @()
                            for ($i = 0; $i -lt $config.ISOPath.Count; $i++) {
                                $currentPath = $config.ISOPath[$i]
                                $isoOptions += [pscustomobject]@{
                                    Label = $currentPath
                                    Action = {
                                        $config.ISOPath = @($config.ISOPath | Where-Object { $_ -ne $currentPath })
                                        Save-HyperVConfig $config
                                        Write-Host "ISO 镜像目录已删除。" -ForegroundColor Green
                                        Start-Sleep -Seconds 2
                                    }
                                }
                            }
                            $selection = Interactive-Selection -Items $isoOptions -DisplayItem { param($item) $item.Label } -Header "请选择要删除的 ISO 目录（使用上下箭头移动，空格键选择，回车确认）或直接回车添加新目录（按 ESC 键返回上级）：" -Mode "Single"
                            if ($selection.IsBack) {
                return
                            } elseif ($selection.Result) {
                                & $selection.Result.Action
        } else {
                                # 添加新目录
                                $newIso = Read-Host "请输入新的 ISO 镜像目录"
                                if (-not [string]::IsNullOrWhiteSpace($newIso)) {
                                    Ensure-Directory -Path $newIso -Description "ISO 镜像目录"
                                    if (-not $config.ISOPath) { $config.ISOPath = @() }
                                    $config.ISOPath += $newIso
                                    Save-HyperVConfig $config
                                    Write-Host "ISO 镜像目录已添加。" -ForegroundColor Green
                                    Start-Sleep -Seconds 2
                                }
                            }
    } else {
                            Write-Host "未设置任何 ISO 镜像目录"
                            $newIso = Read-Host "请输入新的 ISO 镜像目录"
                            if (-not [string]::IsNullOrWhiteSpace($newIso)) {
                                Ensure-Directory -Path $newIso -Description "ISO 镜像目录"
                                if (-not $config.ISOPath) { $config.ISOPath = @() }
                                $config.ISOPath += $newIso
                                Save-HyperVConfig $config
                                Write-Host "ISO 镜像目录已添加。" -ForegroundColor Green
                                Start-Sleep -Seconds 2
                            }
                        }
                    }
                }
            }
        )

        $selection = Interactive-Selection -Items $options -DisplayItem { param($item) $item.Label } -Header "请选择操作（使用上下箭头移动，空格键选择，回车确认；按 ESC 键返回主菜单）：" -Mode "Single" -ExtraInfo $extraInfo
        
        if ($selection.IsBack) {
            break
        } elseif ($selection.Result) {
            & $selection.Result.Action
        }
    }
}

# ---------------- 新增：虚拟机电源管理 ----------------
function Manage-VMPowerState {
    while ($true) {
        # 获取所有虚拟机
        $vms = Invoke-Graceful { Get-VM | Sort-Object Name } "获取虚拟机列表失败" "" @()
        if (-not $vms -or $vms.Count -eq 0) {
            Write-Host "当前未检测到任何虚拟机。"
            return
        }

        # 创建虚拟机选项，包含当前状态信息
        $vmOptions = @()
        foreach ($vm in $vms) {
            $stateIcon = switch ($vm.State) {
                'Running' { '●' }  # 运行中
                'Off' { '○' }      # 已关闭
                'Saved' { '◐' }    # 已保存
                'Paused' { '◑' }   # 已暂停
                default { '?' }    # 未知状态
            }

            $vmOptions += [pscustomobject]@{
                VM = $vm
                DisplayName = "$stateIcon $($vm.Name) [$($vm.State)]"
                CurrentlyRunning = ($vm.State -eq 'Running')
            }
        }

        # 使用通用交互函数进行单选
        $header = @"
虚拟机电源管理
══════════════════════════════════════════════════════════════════
独立控制单个虚拟机的电源状态
● = 运行中  ○ = 已关闭  ◐ = 已保存  ◑ = 已暂停

请选择要控制的虚拟机（使用上/下箭头移动，回车确认；按 ESC 返回主菜单）：
"@

        $vmResult = Interactive-Selection -Items $vmOptions -DisplayItem {
            param($option) $option.DisplayName
        } -Header $header -Mode "Single" -RefreshMode "Full"

        # 如果用户按ESC，返回主菜单
        if ($vmResult.IsBack -or -not $vmResult.Result) {
            Write-Host "返回主菜单。" -ForegroundColor Yellow
            return
        }

        $selectedVM = $vmResult.Result.VM
        $currentState = $selectedVM.State

        # 清屏并显示操作界面
        Clear-Host
        Write-Host "虚拟机电源控制 - $($selectedVM.Name)" -ForegroundColor Yellow
        Write-Host "══════════════════════════════════════════════════" -ForegroundColor DarkCyan
        Write-Host "当前状态：$currentState" -ForegroundColor Cyan

        # 根据当前状态提供相应的操作选项
        $actions = @()

        if ($currentState -eq 'Running') {
            $actions += [pscustomobject]@{ Label = "关闭虚拟机"; Action = "Stop" }
            $actions += [pscustomobject]@{ Label = "保存虚拟机"; Action = "Save" }
            $actions += [pscustomobject]@{ Label = "暂停虚拟机"; Action = "Suspend" }
        } elseif ($currentState -eq 'Off') {
            $actions += [pscustomobject]@{ Label = "启动虚拟机"; Action = "Start" }
        } elseif ($currentState -eq 'Saved') {
            $actions += [pscustomobject]@{ Label = "启动虚拟机（从保存状态）"; Action = "Start" }
            $actions += [pscustomobject]@{ Label = "关闭虚拟机（丢弃保存状态）"; Action = "Stop" }
        } elseif ($currentState -eq 'Paused') {
            $actions += [pscustomobject]@{ Label = "恢复虚拟机"; Action = "Resume" }
            $actions += [pscustomobject]@{ Label = "关闭虚拟机"; Action = "Stop" }
        }

        if ($actions.Count -eq 0) {
            Write-Host "当前状态下没有可用的操作。" -ForegroundColor Yellow
            Write-Host "按任意键继续..."
            [void][System.Console]::ReadKey($true)
            continue
        }

        # 让用户选择操作
        $actionResult = Interactive-Selection -Items $actions -DisplayItem {
            param($action) $action.Label
        } -Header "请选择要执行的操作（使用上/下箭头移动，回车确认；按 ESC 返回虚拟机列表）：" -Mode "Single"

        if ($actionResult.IsBack -or -not $actionResult.Result) {
            continue  # 返回虚拟机列表
        }

        $selectedAction = $actionResult.Result.Action

        # 执行选定的操作
        Write-Host "`n正在执行操作..." -ForegroundColor Cyan

        $success = $false
        switch ($selectedAction) {
            "Start" {
                # 检查内存资源
                $vmMemory = $selectedVM.MemoryStartup
                $hostMemory = (Get-CimInstance Win32_OperatingSystem).FreePhysicalMemory * 1KB

                if ($vmMemory -gt $hostMemory) {
                    Write-Host "启动虚拟机 '$($selectedVM.Name)' 失败：系统可用内存不足" -ForegroundColor Red
                    Write-Host "虚拟机所需：$([math]::Round($vmMemory/1GB, 2)) GB" -ForegroundColor Red
                    Write-Host "系统可用：$([math]::Round($hostMemory/1GB, 2)) GB" -ForegroundColor Red
                    Write-Host "建议：关闭其他虚拟机或减少此虚拟机的内存分配" -ForegroundColor Yellow
                } else {
                    Write-Host "正在启动虚拟机 '$($selectedVM.Name)'..." -ForegroundColor Yellow

                    $success = Invoke-Graceful {
                        Start-VM -Name $selectedVM.Name -ErrorAction Stop

                        # 等待启动完成（最多等待10秒）
                        $timeout = 20; $attempts = 0  # 10秒 * 500ms = 20次检查

                        while ($attempts -lt $timeout) {
                            Start-Sleep -Milliseconds 500
                            $currentVMState = (Get-VM -Name $selectedVM.Name -ErrorAction SilentlyContinue).State
                            if ($currentVMState -eq 'Running') {
                                break
                            }
                            $attempts++
                        }

                        if ($attempts -ge $timeout) {
                            throw "启动超时（等待时间超过10秒）"
                        }

                        $true
                    } "启动虚拟机 '$($selectedVM.Name)' 失败" "" $false

                    if ($success) {
                        Write-Host "虚拟机 '$($selectedVM.Name)' 已成功启动。" -ForegroundColor Green
                    }
                }
            }
            "Stop" {
                Write-Host "正在关闭虚拟机 '$($selectedVM.Name)'..." -ForegroundColor Yellow

                $success = Invoke-Graceful {
                    Stop-VM -Name $selectedVM.Name -Force -ErrorAction Stop
                    $true
                } "关闭虚拟机 '$($selectedVM.Name)' 失败" "" $false

                if ($success) {
                    Write-Host "虚拟机 '$($selectedVM.Name)' 已成功关闭。" -ForegroundColor Green
                }
            }
            "Save" {
                Write-Host "正在保存虚拟机 '$($selectedVM.Name)'..." -ForegroundColor Yellow

                $success = Invoke-Graceful {
                    Save-VM -Name $selectedVM.Name -ErrorAction Stop
                    $true
                } "保存虚拟机 '$($selectedVM.Name)' 失败" "" $false

                if ($success) {
                    Write-Host "虚拟机 '$($selectedVM.Name)' 已成功保存。" -ForegroundColor Green
                }
            }
            "Suspend" {
                Write-Host "正在暂停虚拟机 '$($selectedVM.Name)'..." -ForegroundColor Yellow

                $success = Invoke-Graceful {
                    Suspend-VM -Name $selectedVM.Name -ErrorAction Stop
                    $true
                } "暂停虚拟机 '$($selectedVM.Name)' 失败" "" $false

                if ($success) {
                    Write-Host "虚拟机 '$($selectedVM.Name)' 已成功暂停。" -ForegroundColor Green
                }
            }
            "Resume" {
                Write-Host "正在恢复虚拟机 '$($selectedVM.Name)'..." -ForegroundColor Yellow

                $success = Invoke-Graceful {
                    Resume-VM -Name $selectedVM.Name -ErrorAction Stop
                    $true
                } "恢复虚拟机 '$($selectedVM.Name)' 失败" "" $false

                if ($success) {
                    Write-Host "虚拟机 '$($selectedVM.Name)' 已成功恢复。" -ForegroundColor Green
                }
            }
        }

        Write-Host "`n按任意键继续..."
        [void][System.Console]::ReadKey($true)
    }
}

# ---------------- 新增：网络管理（交互式风格） ----------------
function Manage-Network {
    while ($true) {
        $switches = Get-VMSwitch | Sort-Object Name
        # 类型映射表
        $typeMap = @{ 'External' = '外部'; 'Internal' = '内部'; 'Private' = '专用' }
        
        # 美化显示逻辑，使用固定宽度格式化，避免显示凌乱
        $extraInfo = "`n当前虚拟交换机:`n══════════════════════════════════════════════════════════════════`n"
        
        # 一次性获取所有虚拟网卡信息，减少重复查询造成的延迟
        $vNetAdapters = @{}; $vNetIPs = @{}
        
        try {
            # 一次性获取所有虚拟网卡，减少查询次数
            $allVNetAdapters = Get-NetAdapter | Where-Object { $_.Virtual -eq $true } | ForEach-Object {
                $vNetAdapters[$_.Name] = $_
            }
            
            # 一次性获取所有IPv4地址，减少查询次数
            $allIPv4 = Get-NetIPAddress -AddressFamily IPv4 -ErrorAction SilentlyContinue
            foreach ($ip in $allIPv4) {
                $vNetIPs[$ip.InterfaceAlias] = $ip
            }
        } catch {
            # 忽略错误，确保界面能正常显示
        }
        
        # 定义固定的列边界位置（不是列宽度）
        $col1 = 0; $col2 = 25; $col3 = 45     # 名称列、类型列、IP地址列开始位置
        
        # 添加表头
        if ($switches -and $switches.Count -gt 0) {
            # 创建表头行 - 使用精确的位置放置文本
            $headerLine = "名称".PadRight($col2)
            $headerLine += "类型".PadRight($col3 - $col2)
            $headerLine += "IP地址"
            $extraInfo += $headerLine + "`n"
            
            # 创建分隔线 - 完全控制每个字符的位置
            $separator = "".PadRight($col3 + 25, "-")
            # 在列边界处使用"+"交叉符号，更清晰地表示列边界
            $separator = $separator.Remove($col2, 1).Insert($col2, "+")
            $separator = $separator.Remove($col3, 1).Insert($col3, "+")
            $extraInfo += $separator + "`n"
            
            foreach ($sw in $switches) {
                $type = if ($typeMap.ContainsKey($sw.SwitchType.ToString())) { $typeMap[$sw.SwitchType.ToString()] } else { $sw.SwitchType }
                
                # 获取IP信息，使用预先查询的缓存数据
                $ipInfo = "-"
                $adapterName = "vEthernet ($($sw.Name))"
                
                if ($vNetAdapters.ContainsKey($adapterName) -and $vNetIPs.ContainsKey($adapterName)) {
                    $ipAddress = $vNetIPs[$adapterName]
                    if ($ipAddress) {
                        $ipInfo = "$($ipAddress.IPAddress)/$($ipAddress.PrefixLength)"
                    }
                }
                
                # 创建数据行 - 使用与表头完全相同的位置布局
                $line = $sw.Name.PadRight($col2)
                if ($line.Length > $col2) { $line = $line.Substring(0, $col2 - 3) + "..." }
                
                $typeText = "[$type]"
                $col2End = $col3 - 1
                $typeSpacing = $col2End - $col2 - $typeText.Length
                if ($typeSpacing -lt 0) { $typeSpacing = 0 }
                
                $line += $typeText + " ".PadRight($typeSpacing)
                $line += $ipInfo
                
                $extraInfo += $line + "`n"
            }
        } else {
            $extraInfo += "无虚拟交换机`n"
        }
        
        $extraInfo += "══════════════════════════════════════════════════════════════════"
        $options = @(
            [pscustomobject]@{
                Label = "新建虚拟交换机"
                Action = {
                    while ($true) {
                        $typeOptions = @(
                            [pscustomobject]@{ Label = "外部"; Value = "External" },
                            [pscustomobject]@{ Label = "内部"; Value = "Internal" },
                            [pscustomobject]@{ Label = "专用"; Value = "Private" }
                        )
                        $typeSel = Interactive-Selection -Items $typeOptions -DisplayItem { param($item) $item.Label } -Header "请选择虚拟交换机类型（按 ESC 返回上级）：" -Mode "Single"
                        if ($typeSel.IsBack) { break }
                        if ($typeSel.Result) {
                            $type = $typeSel.Result.Value
                            if ($type -eq "External") {
                                # 外部交换机
                                $adapters = Get-NetAdapter | Where-Object { $_.Status -eq 'Up' -and $_.Virtual -eq $false }
                                if (-not $adapters -or $adapters.Count -eq 0) {
                                    Write-Host "未检测到可用物理网卡，无法创建外部交换机。" -ForegroundColor Red
                                    break
                                }
                                $adapterSel = Interactive-Selection -Items $adapters -DisplayItem { param($a) "$($a.Name) ($($a.InterfaceDescription))" } -Header "请选择要绑定的物理网卡（按 ESC 返回上级）：" -Mode "Single"
                                if ($adapterSel.IsBack -or -not $adapterSel.Result) { continue }
                                $swName = Read-Host "请输入交换机名称（回车使用默认：ExternalSwitch）"
                                if ([string]::IsNullOrWhiteSpace($swName)) { $swName = "ExternalSwitch" }
                                $result = Invoke-Graceful {
                                    New-VMSwitch -Name $swName -NetAdapterName $adapterSel.Result.Name -AllowManagementOS $true -ErrorAction Stop
                                    Write-Host "外部交换机 '$swName' 创建成功。" -ForegroundColor Green
                                    $true
                                } "创建外部交换机失败" "" $false
                                if (-not $result) { continue }

                                # 禁用 LSO v2 (IPv4/IPv6) 选项
                                Invoke-Graceful {
                                    # 获取虚拟网络适配器名称
                                    $vEthernetName = "vEthernet ($swName)"
                                    Write-Host "正在禁用 LSO v2 选项..." -ForegroundColor Yellow

                                    # 禁用 IPv4 和 IPv6 的 LSO v2
                                    Disable-NetAdapterLso -Name $vEthernetName -IPv4 -IPv6 -ErrorAction Stop
                                    Write-Host "已成功禁用 LSO v2 (IPv4/IPv6) 选项。" -ForegroundColor Green
                                } "禁用 LSO v2 选项失败，但交换机创建成功" -NoReturn
                                break
                            } elseif ($type -eq "Internal") {
                                # 内部交换机
                                while ($true) {
                                    $swName = Read-Host "请输入交换机名称（回车使用默认：InternalSwitch）"
                                    if ([string]::IsNullOrWhiteSpace($swName)) { $swName = "InternalSwitch" }
                                    $result = Invoke-Graceful {
                                        $sw = New-VMSwitch -Name $swName -SwitchType Internal -ErrorAction Stop
                                        Write-Host "内部交换机 '$swName' 创建成功。" -ForegroundColor Green
                                        $true
                                    } "创建内部交换机失败" "" $false
                                    if (-not $result) {
                                        $retry = Read-Host "是否重试？(Y/n)："
                                        if ($retry -match '^(N|n)$') { break }
                                        else { continue }
                                    }
                                    # 获取虚拟网卡
                                    Start-Sleep -Milliseconds 500
                                    $vNic = Get-NetAdapter | Where-Object { $_.Name -like "vEthernet*" -and $_.Name -like "*$swName*" }
                                    if (-not $vNic) {
                                        Write-Host "未找到对应的虚拟网卡，无法配置IP。" -ForegroundColor Red
                                        $retry = Read-Host "是否重试？(Y/n)："
                                        if ($retry -match '^(N|n)$') { break }
                                        else { continue }
                                    }
                                    # 建议网段
                                    $rand = New-Object System.Random
                                    $randNum = $rand.Next(100,200)
                                    $defaultSubnet = "192.168.$randNum.1/24"
                                    while ($true) {
                                        $ipInput = Read-Host "请输入虚拟网卡IP（如*************/24，回车使用建议：$defaultSubnet）"
                                        if ([string]::IsNullOrWhiteSpace($ipInput)) { $ipInput = $defaultSubnet }
                                        $ip, $prefix = $ipInput -split "/"
                                        if (-not $prefix) { $prefix = "24" }
                                        $result = Invoke-Graceful {
                                            # 先移除已存在的IP
                                            $oldIp = Get-NetIPAddress -InterfaceAlias $vNic.Name -ErrorAction SilentlyContinue
                                            if ($oldIp) { $oldIp | Remove-NetIPAddress -Confirm:$false -ErrorAction SilentlyContinue }
                                            New-NetIPAddress -InterfaceAlias $vNic.Name -IPAddress $ip -PrefixLength $prefix -DefaultGateway $ip -ErrorAction Stop
                                            Write-Host "已为虚拟网卡 $($vNic.Name) 配置IP $ip/$prefix。" -ForegroundColor Green
                                            $true
                                        } "配置虚拟网卡IP失败" "" $false
                                        if (-not $result) {
                                            $retry = Read-Host "是否重试？(Y/n)："
                                            if ($retry -match '^(N|n)$') { break }
                                            continue
                                        }
                                        break
                                    }
                                    $enableNat = Read-Host "是否自动启用NAT（Y/n）："
                                    if ([string]::IsNullOrWhiteSpace($enableNat) -or $enableNat -match '^(Y|y)$') {
                                        $natName = "NAT_$swName"
                                        $result = Invoke-Graceful {
                                            # 先移除同名NAT
                                            $oldNat = Get-NetNat -Name $natName -ErrorAction SilentlyContinue
                                            if ($oldNat) { Remove-NetNat -Name $natName -Confirm:$false -ErrorAction SilentlyContinue }
                                            New-NetNat -Name $natName -InternalIPInterfaceAddressPrefix ("$ip/$prefix") -ErrorAction Stop
                                            Write-Host "NAT '$natName' 已启用。" -ForegroundColor Green
                                            $true
                                        } "启用NAT失败" "" $false
                                    }
                                    $enableForward = Read-Host "是否启用IP转发（Y/n）："
                                    if ([string]::IsNullOrWhiteSpace($enableForward) -or $enableForward -match '^(Y|y)$') {
                                        $result = Invoke-Graceful {
                                            Set-NetIPInterface -InterfaceAlias $vNic.Name -Forwarding Enabled -ErrorAction Stop
                                            Write-Host "IP转发已启用。" -ForegroundColor Green
                                            $true
                                        } "启用IP转发失败" "" $false
                                    }
                                    # 检查配置结果
                                    Write-Host "【配置检测结果】" -ForegroundColor Cyan
                                    $ipInfo = Invoke-Graceful {
                                        $info = Get-NetIPAddress -InterfaceAlias $vNic.Name -ErrorAction Stop
                                        Write-Host "虚拟网卡IP信息："
                                        $info | Format-Table IPAddress, PrefixLength, AddressFamily, InterfaceAlias -AutoSize | Out-String | Write-Host
                                        $info
                                    } "无法获取虚拟网卡IP信息" "" $null

                                    $natInfo = Invoke-Graceful {
                                        $info = Get-NetNat -ErrorAction Stop | Where-Object { $_.InternalIPInterfaceAddressPrefix -like "$ip/*" }
                                        if ($info) {
                                            Write-Host "NAT 配置："
                                            $info | Format-Table Name, InternalIPInterfaceAddressPrefix, NatGatewayAddress -AutoSize | Out-String | Write-Host
                                        } else {
                                            Write-Host "未检测到匹配的NAT配置。" -ForegroundColor Yellow
                                        }
                                        $info
                                    } "无法获取NAT信息" "" $null

                                    $fwdInfo = Invoke-Graceful {
                                        $info = Get-NetIPInterface -InterfaceAlias $vNic.Name -ErrorAction Stop
                                        Write-Host "IP转发状态："
                                        $fwdTable = $info | Format-Table InterfaceAlias, Forwarding -AutoSize | Out-String
                                        Write-Host $fwdTable
                                        if ($info.Forwarding -notcontains 'Enabled') {
                                            Write-Host "[警告] IP转发未生效，请检查系统设置。" -ForegroundColor Yellow
                                        }
                                        $info
                                    } "无法获取IP转发状态" "" $null
                                    
                                    # 修改部分：优化等待逻辑，减少卡顿，防止界面卡住
                                    Write-Host "`n请确认上方配置，按任意键返回上级..." -ForegroundColor Yellow
                                    
                                    # 使用ReadKey代替ReadLine，添加超时机制，防止界面卡住
                                    Invoke-Graceful {
                                        # 先清空任何现有的键盘输入
                                        while ([Console]::KeyAvailable) {
                                            [void][Console]::ReadKey($true)
                                        }
                                        # 等待按键，最多等待3秒
                                        $waitStartTime = Get-Date
                                        while (-not [Console]::KeyAvailable -and ((Get-Date) - $waitStartTime).TotalSeconds -lt 3) {
                                            Start-Sleep -Milliseconds 100
                                        }
                                        # 如果有按键，读取它；如果超时，也继续执行
                                        if ([Console]::KeyAvailable) {
                                            [void][Console]::ReadKey($true)
                                        }
                                    } "等待用户输入时发生错误" -NoReturn

                                    # 直接返回主菜单
                                    return
                                }
                            } elseif ($type -eq "Private") {
                                # 专用交换机
                                $swName = Read-Host "请输入交换机名称（回车使用默认：PrivateSwitch）"
                                if ([string]::IsNullOrWhiteSpace($swName)) { $swName = "PrivateSwitch" }
                                $result = Invoke-Graceful {
                                    New-VMSwitch -Name $swName -SwitchType Private -ErrorAction Stop
                                    Write-Host "专用交换机 '$swName' 创建成功。" -ForegroundColor Green
                                    $true
                                } "创建专用交换机失败" "" $false
                                if (-not $result) {
                                    Start-Sleep -Seconds 2
                                    return
                                }
                                Start-Sleep -Seconds 2
                                return
                            }
                        }
                    }
                    return
                }
            },
            [pscustomobject]@{
                Label = "删除虚拟交换机"
                Action = {
                    $switches = Get-VMSwitch | Sort-Object Name
                    if (-not $switches -or $switches.Count -eq 0) {
                        Write-Host "当前没有可删除的虚拟交换机。" -ForegroundColor Yellow
                        Start-Sleep -Seconds 2
                        return
                    }
                    # 类型映射表，用于显示
                    $typeMap = @{ 'External' = '外部'; 'Internal' = '内部'; 'Private' = '专用' }
                    
                    # 调用多选函数
                    $delSel = Interactive-Selection -Items $switches -DisplayItem { param($sw) "$($sw.Name) [$($typeMap[$sw.SwitchType.ToString()])]" } -Header "请选择要删除的交换机（空格切换，回车确认，按 ESC 返回上级）：" -Mode "Multi" -RefreshMode "Partial"
                    
                    # 检查是否有选择
                    if (-not $delSel -or $delSel.Count -eq 0) { return }

                    foreach ($sw in $delSel) {
                        Write-Host "`n正在处理交换机: $($sw.Name)..." -ForegroundColor Yellow
                        
                        # 检查是否为内部交换机，如果是，则尝试清理关联资源
                        if ($sw.SwitchType -eq 'Internal') {
                            $adapterName = "vEthernet ($($sw.Name))"
                            $adapter = Get-NetAdapter -Name $adapterName -ErrorAction SilentlyContinue
                            
                            if ($adapter) {
                                Write-Host "  - 正在查找并删除关联的 NAT 规则..." -ForegroundColor Cyan
                                $natName = "NAT_$($sw.Name)"
                                $nat = Get-NetNat -Name $natName -ErrorAction SilentlyContinue
                                if ($nat) {
                                    Invoke-Graceful {
                                        Remove-NetNat -Name $natName -Confirm:$false -ErrorAction Stop
                                        Write-Host "    - NAT 规则 '$natName' 已删除。" -ForegroundColor Green
                                    } "删除 NAT 规则失败" -NoReturn
                                } else {
                                    Write-Host "    - 未找到名为 '$natName' 的 NAT 规则。"
                                }

                                Write-Host "  - 正在禁用关联虚拟网卡的 IP 转发..." -ForegroundColor Cyan
                                Invoke-Graceful {
                                    Set-NetIPInterface -InterfaceAlias $adapter.Name -Forwarding Disabled -ErrorAction Stop
                                    Write-Host "    - 虚拟网卡 '$($adapter.Name)' 的 IP 转发已禁用。" -ForegroundColor Green
                                } "禁用虚拟网卡 IP 转发失败" -NoReturn
                            } else {
                                Write-Host "  - 未找到关联的虚拟网卡 '$adapterName'，跳过 NAT 和转发清理。"
                            }
                        }
                        
                        # 删除虚拟交换机
                        Write-Host "  - 正在删除虚拟交换机 '$($sw.Name)'..." -ForegroundColor Cyan
                        Invoke-Graceful {
                            Remove-VMSwitch -Name $sw.Name -Force -ErrorAction Stop
                            Write-Host "    - 虚拟交换机 '$($sw.Name)' 已成功删除。" -ForegroundColor Green
                        } "删除虚拟交换机失败" -NoReturn
                    }
                    Write-Host "`n删除操作完成，按回车键继续..."
                    [void][System.Console]::ReadKey($true)
                }
            }
        )

        $selection = Interactive-Selection -Items $options -DisplayItem { param($item) $item.Label } -Header "请选择操作（使用上下箭头移动，空格键选择，回车确认；按 ESC 键返回主菜单）：" -Mode "Single" -ExtraInfo $extraInfo
        
        if ($selection.IsBack) {
            break
        } elseif ($selection.Result) {
            & $selection.Result.Action
        }
    }
}

function Manage-Other {
    $options = @(
        [pscustomobject]@{ Label = "嵌套虚拟化"; Action = { Toggle-Nested } },
        [pscustomobject]@{ Label = "资源计量"; Action = { Toggle-Resource } }
    )

    $selection = Interactive-Selection -Items $options -DisplayItem { param($item) $item.Label } -Header "请选择其他设置操作（使用上下箭头移动，空格键选择，回车确认；按 ESC 键返回主菜单）：" -Mode "Single"

    if ($selection.IsBack) {
        return
    } elseif ($selection.Result) {
        & $selection.Result.Action
    }
}

# ---------------- 主菜单显示与循环 ----------------
function Show-Menu {
    Write-Host "`n【主菜单】" -ForegroundColor Yellow
    Write-Host "══════════════════════════════════════════════════" -ForegroundColor DarkCyan
    Write-Host "1. 管理" -ForegroundColor Cyan
    Write-Host "2. 网络" -ForegroundColor Cyan
    Write-Host "3. 磁盘" -ForegroundColor Cyan
    Write-Host "4. 镜像" -ForegroundColor Cyan
    Write-Host "5. 列表" -ForegroundColor Cyan
    Write-Host "6. 电源" -ForegroundColor Cyan
    Write-Host "7. 目录" -ForegroundColor Cyan
    Write-Host "8. 其他" -ForegroundColor Cyan
    Write-Host "9. 控制台" -ForegroundColor Cyan
    Write-Host "0. 退出" -ForegroundColor Cyan
    Write-Host "══════════════════════════════════════════════════" -ForegroundColor DarkCyan
}

function Manage-Disks {
    $options = @(
        [pscustomobject]@{ Label = "创建"; Action = { Add-VirtualDisk } },
        [pscustomobject]@{ Label = "挂载"; Action = { Mount-VirtualDisk } },
        [pscustomobject]@{ Label = "移除"; Action = { Unmount-VirtualDisk } },
        [pscustomobject]@{ Label = "删除"; Action = { Remove-VirtualDisks } }
    )
    $selection = Interactive-Selection -Items $options -DisplayItem { param($opt) "$($opt.Label)" } -TransformReturn { param($opt) $opt } -Header "请选择虚拟机管理操作`n（使用上/下箭头移动，按空格键选择（记录顺序），回车确认, 无选择则返回上级）" -Mode "Ordered" -RefreshMode "Full"
    if (-not $selection -or $selection.Count -eq 0) {
         Write-Host "未选择任何操作，返回上级。"
         return
    }
    foreach ($sel in $selection) {
        & $sel.Action
    }
}

do {
    Show-Menu
    Write-Host "请选择 (0-9)：" -NoNewline -ForegroundColor Cyan
    $opt = Read-Host
    switch ($opt) {
        '1' { Manage-VM }           # 管理
        '2' { Manage-Network }      # 网络
        '3' { Manage-Disks }        # 磁盘
        '4' { Manage-ISO }          # 镜像
        '5' { ListVMs }             # 列表
        '6' { Manage-VMPowerState } # 电源
        '7' { Set-BaseDirs }        # 目录
        '8' { Manage-Other }        # 其他
        '9' { Open-HyperVConsole }  # 控制台
        '0' { Write-Host "退出脚本。"; break }
        default { Write-Host "选项无效，请重试。" -ForegroundColor Red }
    }
} while ($true)
