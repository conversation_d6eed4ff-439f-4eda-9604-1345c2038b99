# LSO v2 禁用功能说明

## 功能概述

在功能2（创建外部交换机）中新增了自动禁用 LSO v2 (Large Send Offload Version 2) 选项的功能。当外部交换机创建成功后，系统会自动禁用该交换机对应虚拟网络适配器的 IPv4 和 IPv6 LSO v2 选项。

## 修改内容

### 代码位置
- 文件：`HyperV_Manage.ps1`
- 函数：`Manage-Network` -> 新建虚拟交换机 -> 外部交换机创建流程
- 行号：2420-2429

### 新增代码
```powershell
# 禁用 LSO v2 (IPv4/IPv6) 选项
Invoke-Graceful {
    # 获取虚拟网络适配器名称
    $vEthernetName = "vEthernet ($swName)"
    Write-Host "正在禁用 LSO v2 选项..." -ForegroundColor Yellow
    
    # 禁用 IPv4 和 IPv6 的 LSO v2
    Disable-NetAdapterLso -Name $vEthernetName -IPv4 -IPv6 -ErrorAction Stop
    Write-Host "已成功禁用 LSO v2 (IPv4/IPv6) 选项。" -ForegroundColor Green
} "禁用 LSO v2 选项失败，但交换机创建成功" -NoReturn
```

## 功能特点

### 1. 自动执行
- 外部交换机创建成功后自动执行
- 无需用户手动干预
- 不影响交换机的正常创建流程

### 2. 错误处理
- 使用 `Invoke-Graceful` 包装，确保即使禁用失败也不影响交换机创建
- 提供清晰的状态提示信息
- 失败时显示友好的错误消息

### 3. 用户反馈
- 执行过程中显示进度提示
- 成功时显示确认消息
- 失败时显示错误信息但不中断流程

## LSO v2 说明

### 什么是 LSO v2
Large Send Offload Version 2 (LSO v2) 是一种网络优化技术，允许网络适配器硬件处理大数据包的分段工作，而不是由 TCP/IP 协议栈处理。

### 为什么要禁用
1. **兼容性问题**：某些虚拟化环境下可能导致网络性能问题
2. **网络调试**：禁用后更容易进行网络问题诊断
3. **稳定性**：在某些配置下可能提高网络连接的稳定性

### 影响范围
- 仅影响新创建的外部交换机对应的虚拟网络适配器
- 不影响物理网络适配器的 LSO 设置
- 不影响其他类型的虚拟交换机（内部、专用）

## 使用方法

### 正常使用
1. 运行 `HyperV_Manage.ps1`
2. 选择功能2（网络管理）
3. 选择"新建虚拟交换机"
4. 选择"外部"类型
5. 按提示选择物理网卡和输入交换机名称
6. 系统会自动创建交换机并禁用 LSO v2

### 验证结果
可以使用提供的测试脚本验证功能：
```powershell
.\test_lso_disable.ps1
```

或手动检查：
```powershell
# 查看虚拟网络适配器的 LSO 设置
Get-NetAdapterLso -Name "vEthernet (交换机名称)"
```

## 注意事项

1. **权限要求**：需要管理员权限才能执行 LSO 设置修改
2. **网络适配器名称**：虚拟网络适配器名称格式为 `vEthernet (交换机名称)`
3. **错误处理**：如果禁用失败，交换机仍然会成功创建，只是 LSO v2 保持启用状态
4. **重启要求**：某些情况下可能需要重启网络适配器才能使设置生效

## 故障排除

### 常见问题
1. **权限不足**：确保以管理员身份运行脚本
2. **适配器未找到**：检查交换机名称是否正确
3. **命令不支持**：确保 Windows 版本支持 `Disable-NetAdapterLso` 命令

### 手动禁用方法
如果自动禁用失败，可以手动执行：
```powershell
# 替换 "交换机名称" 为实际的交换机名称
Disable-NetAdapterLso -Name "vEthernet (交换机名称)" -IPv4 -IPv6
```

## 相关命令

- `Get-NetAdapterLso`：查看 LSO 设置
- `Disable-NetAdapterLso`：禁用 LSO
- `Enable-NetAdapterLso`：启用 LSO
- `Set-NetAdapterLso`：设置特定的 LSO 选项
