# 测试脚本：验证 LSO v2 禁用功能
# 此脚本用于测试外部交换机创建后是否正确禁用了 LSO v2 选项

Write-Host "=== LSO v2 禁用功能测试 ===" -ForegroundColor Cyan

# 检查是否有管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "此测试需要管理员权限。请以管理员身份运行 PowerShell。" -ForegroundColor Red
    exit 1
}

# 获取所有虚拟网络适配器
Write-Host "`n1. 检查当前虚拟网络适配器..." -ForegroundColor Yellow
$vEthernetAdapters = Get-NetAdapter | Where-Object { $_.Name -like "vEthernet*" }

if ($vEthernetAdapters.Count -eq 0) {
    Write-Host "未找到任何虚拟网络适配器 (vEthernet)。" -ForegroundColor Yellow
    Write-Host "请先创建一个外部交换机来测试此功能。" -ForegroundColor Yellow
    exit 0
}

Write-Host "找到 $($vEthernetAdapters.Count) 个虚拟网络适配器：" -ForegroundColor Green
foreach ($adapter in $vEthernetAdapters) {
    Write-Host "  - $($adapter.Name)" -ForegroundColor White
}

# 检查每个适配器的 LSO 设置
Write-Host "`n2. 检查 LSO v2 设置..." -ForegroundColor Yellow
foreach ($adapter in $vEthernetAdapters) {
    Write-Host "`n检查适配器: $($adapter.Name)" -ForegroundColor Cyan
    
    try {
        $lsoSettings = Get-NetAdapterLso -Name $adapter.Name -ErrorAction Stop
        
        Write-Host "  IPv4 LSO 状态: $($lsoSettings.V1IPv4Enabled)" -ForegroundColor $(if ($lsoSettings.V1IPv4Enabled) { "Red" } else { "Green" })
        Write-Host "  IPv6 LSO 状态: $($lsoSettings.V1IPv6Enabled)" -ForegroundColor $(if ($lsoSettings.V1IPv6Enabled) { "Red" } else { "Green" })
        
        if ($lsoSettings.V1IPv4Enabled -or $lsoSettings.V1IPv6Enabled) {
            Write-Host "  ⚠️  LSO v2 仍然启用" -ForegroundColor Yellow
        } else {
            Write-Host "  ✅ LSO v2 已正确禁用" -ForegroundColor Green
        }
        
    } catch {
        Write-Host "  ❌ 无法获取 LSO 设置: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试禁用命令
Write-Host "`n3. 测试 LSO v2 禁用命令..." -ForegroundColor Yellow
$testAdapter = $vEthernetAdapters[0]
Write-Host "使用适配器 '$($testAdapter.Name)' 进行测试" -ForegroundColor Cyan

try {
    Write-Host "执行禁用命令..." -ForegroundColor Yellow
    Disable-NetAdapterLso -Name $testAdapter.Name -IPv4 -IPv6 -ErrorAction Stop
    Write-Host "✅ 命令执行成功" -ForegroundColor Green
    
    # 验证结果
    Start-Sleep -Seconds 2
    $lsoSettings = Get-NetAdapterLso -Name $testAdapter.Name -ErrorAction Stop
    
    Write-Host "`n验证结果:" -ForegroundColor Cyan
    Write-Host "  IPv4 LSO 状态: $($lsoSettings.V1IPv4Enabled)" -ForegroundColor $(if ($lsoSettings.V1IPv4Enabled) { "Red" } else { "Green" })
    Write-Host "  IPv6 LSO 状态: $($lsoSettings.V1IPv6Enabled)" -ForegroundColor $(if ($lsoSettings.V1IPv6Enabled) { "Red" } else { "Green" })
    
    if (-not $lsoSettings.V1IPv4Enabled -and -not $lsoSettings.V1IPv6Enabled) {
        Write-Host "✅ LSO v2 禁用功能正常工作" -ForegroundColor Green
    } else {
        Write-Host "❌ LSO v2 禁用功能可能存在问题" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ 禁用命令执行失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Cyan
